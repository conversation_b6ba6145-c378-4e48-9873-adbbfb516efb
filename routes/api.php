<?php

use App\Events\GlobalNotificationEvent;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\AdditionalUserController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\BroadcastController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\ProjectController;
use App\Http\Controllers\ProjectItemController;
use App\Http\Controllers\UserController;
use App\Routes\EmailTemplateRoutes;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::post('/payments-providers/stripe/webhook', [
    App\Http\Controllers\PaymentProviders\StripeController::class,
    'handleWebhook',
])->name('payments-providers.stripe.webhook');

Route::post('/payments-providers/paddle/webhook', [
    App\Http\Controllers\PaymentProviders\PaddleController::class,
    'handleWebhook',
])->name('payments-providers.paddle.webhook');

Route::post('/payments-providers/lemon-squeezy/webhook', [
    App\Http\Controllers\PaymentProviders\LemonSqueezyController::class,
    'handleWebhook',
])->name('payments-providers.lemon-squeezy.webhook');

Route::post(
    '/notify',
    [NotificationController::class, 'send']
);

Route::get('/send-notification',
    [NotificationController::class, 'test']
);

Route::get('/ip-location/{ip}', [
    App\Http\Controllers\Api\IpLocationController::class,
    'getLocationByIp',
])->name('ip-location');

Route::get('/projects/types', [ProjectController::class, 'getProjectTypes']);
Route::get('/projects/check-name', [ProjectController::class, 'checkName']);
Route::get('/projects-item/types', [ProjectItemController::class, 'getProjectItemTypes']);

// PDF generation endpoint for simulation
Route::post('/project-items/generate-pdf', [ProjectItemController::class, 'generatePdf']);

// Protected routes — user must be authenticated
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/projects/users', [ProjectController::class, 'getUserList']);
    Route::apiResource('projects', ProjectController::class);
    Route::apiResource('project-items', ProjectItemController::class);
    EmailTemplateRoutes::define();
});

Route::post('/custom/login', [LoginController::class, 'customLogin'])
    ->name('livewire.custom.login');

Route::post('/custom/login-by-pin', [LoginController::class, 'loginByPin'])
    ->name('livewire.custom.login_by_pin');

Route::post('/custom/check-token', [LoginController::class, 'checkToken'])
    ->name('livewire.custom.check_token');

Route::delete('/custom/token/{id}', [LoginController::class, 'revokeToken'])
    ->name('livewire.custom.delete_token');


Route::middleware('auth:sanctum')->group(function () {
    Route::get('/sub-users', [AdditionalUserController::class, 'index']);
    Route::post('/sub-users', [AdditionalUserController::class, 'store']);
    Route::delete('/sub-users/{id}', [AdditionalUserController::class, 'destroy']);
    Route::get('/active-subscription', [AdditionalUserController::class, 'getCurrentSubscription']);
    Route::post('/sub-users/user-status', [AdditionalUserController::class, 'updateUserStatus']);
    Route::post('/custom/broadcasting/auth', [BroadcastController::class, 'authenticate']);
    Route::get('/test-permission', function () {
        if(auth()->user()->hasPermissionTo('test-permission')) {
            return 'authorized';
        } else {
            return 'not authorized';
        }
    });
});
Route::get('/check-mail', [AdditionalUserController::class, 'checkMail']);


Route::middleware('auth:sanctum')->get('/me', function () {
    return response()->json([
        'user' => auth()->user(),
        'session_id' => session()->getId(),
        'session' => session()->all()
    ]);
});

Route::get('/test-user', function () {
    return response()->json([
        'user' => auth()->user(),
    ]);
});
