APP_NAME=TallStack
APP_DESCRIPTION="TallStack is a SaaS starter kit that helps you build and launch your SaaS product faster."
APP_ENV=local
APP_KEY=base64:TIA4x01Yf6m5oszm43QVDoD91xkPAobYBsVdU4AZpm0=
APP_DEBUG=true
APP_URL=http://localhost:5500
APP_TIMEZONE=UTC

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=sail
DB_PASSWORD=password

# MongoDB (for notifications)
DB_MONGO_CONNECTION=mongodb
DB_MONGO_HOST=localhost
DB_MONGO_PORT=27017
DB_MONGO_DATABASE=dev_notifications_db
DB_MONGO_USERNAME=
DB_MONGO_PASSWORD=

BROADCAST_DRIVER=reverb
BROADCAST_CONNECTION=reverb
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=4320

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_SCHEME=null
MAIL_HOST=ssl0.ovh.net
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD='4U:f]2t%,{_U"}fy'
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_ENCRYPTION=tls
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

PAYMENT_PRORATION_ENABLED=true

GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""

FACEBOOK_CLIENT_ID=""
FACEBOOK_CLIENT_SECRET=""

STRIPE_SECRET_KEY="sk_test_51Ovi4aP2XIGCK9usTg00kj2Q0Ao5BMpDWwBCTtNqAsNSuDPMhEZy92dJYfQHfhWInB72iYGKDjoh3dlCtT0ehijC00qqKtMGDW"
STRIPE_PUBLISHABLE_KEY="pk_test_51Ovi4aP2XIGCK9us3NPDR893prOemTip1V4j4gxzLzYDSby7XMTBIbfRw4cydDJDhZLQDSlaoPJV13W4P4wM1Eoq00aBkbDDCh"
STRIPE_WEBHOOK_SIGNING_SECRET="whsec_58cb4b9134cdd91594b07281cef92f44b326a14f5ee22ecbacdc03af4190c221"

PADDLE_VENDOR_ID=""
PADDLE_CLIENT_SIDE_TOKEN=""
PADDLE_VENDOR_AUTH_CODE=""
PADDLE_PUBLIC_KEY=""
PADDLE_WEBHOOK_SECRET=""
PADDLE_IS_SANDBOX=true

LEMON_SQUEEZY_API_KEY=""
LEMON_SQUEEZY_STORE_ID=""
LEMON_SQUEEZY_SIGNING_SECRET=""
LEMON_SQUEEZY_IS_TEST_MODE=false

GOOGLE_TRACKING_ID="G-XXXXX"
TRACKING_SCRIPTS=""
GOOGLE_ANALYTICS_API_SECRET=""

SOCIAL_FACEBOOK_URL=""
SOCIAL_X_URL=""
SOCIAL_INSTAGRAM_URL=""
SOCIAL_LINKEDIN_URL=""
SOCIAL_YOUTUBE_URL=""
SOCIAL_GITHUB_URL=""
SOCIAL_DISCORD_URL=""

RECAPTCHA_SITE_KEY=""
RECAPTCHA_SECRET_KEY=""
RECAPTCHA_SKIP_IP=""

COOKIE_CONSENT_ENABLED=false

TWILIO_SID=""
TWILIO_TOKEN=""
TWILIO_FROM=""

TRIAL_WITHOUT_PAYMENT_SMS_VERIFICATION_ENABLED=false

# local development settings

APP_PORT=8080
FORWARD_DB_PORT=3307
FORWARD_MAILPIT_PORT=1025
FORWARD_MAILPIT_DASHBOARD_PORT=8025
FORWARD_REDIS_PORT=6379
VITE_PORT=5173
FORWARD_NGROK_PORT=4040

SAIL_XDEBUG_MODE=develop,debug,coverage

NGROK_AUTHTOKEN=
NGROK_STATIC_DOMAIN=

WWWGROUP=1337

REVERB_APP_ID=737175
REVERB_APP_KEY=982uneym666ibh51yf05
REVERB_APP_SECRET=ykllpan905gcko8l0a45
REVERB_HOST="localhost"
REVERB_PORT=8002
REVERB_SCHEME=http

VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"

TRANSLATION_API_URL="https://dev.ts.solar-control.energy"
VITE_TRANSLATION_API_URL="${TRANSLATION_API_URL}"
TRANSLATION_API_KEY="123"
CMS_URL= "https://dev.cms.solar-control.energy"

GOOGLE_MAPS_API_KEY="AIzaSyBiNktNFdhm-Z4OHjqvzKXbwY0lFRGyNTc"
MAPS_URL="https://maps.googleapis.com/maps/api/js?key={googleMapsApiKey}"
GOOGLE_LABELS_LAYER_LINK="https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={zoom}"
GOOGLE_SATELLITE_LAYER_LINK="https://khms2.google.com/kh/v=991?x={x}&y={y}&z={zoom}"
GOOGLE_STATIC_MAPS_URL="https://maps.googleapis.com/maps/api/staticmap?center={lat},{lng}&zoom={zoom}&size={size}&maptype={mapTypeId}&markers=color:red%7Clabel:+%7C{markerLat},{markerLng}&key={googleMapsApiKey}"
ENABLE_STATIC_RESULTS_MAP=true
GOOGLE_MAPS_MAP_ID="4504f8b37365c3d0"
# URL pour l'API Geocode
GOOGLE_GEOCODE_URL="https://maps.googleapis.com/maps/api/geocode/json?address={city},{country}&key={googleMapsApiKey}"
VITE_GOOGLE_MAPS_API_KEY="${GOOGLE_MAPS_API_KEY}"

PVGIS_API="https://dev.api.pvgis.com/api"
IP_LOCATION_URL='http://ip-api.com/json/{ip}'
CDN_URL='https://cdn.solar-control.energy'

CDN_DASHBOARD="dashboard.localhost"

DASHBOARD_URL="http://dashboard.localhost:5500"
ADMIN_URL="http://admin.localhost:5500"
APP_DOMAIN="localhost"
SUBDOMAIN_DASHBOARD="dashboard.localhost"
SUBDOMAIN_ADMIN="admin.localhost"

SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1,localhost:5173,localhost:4200,dashboard.localhost:5500

APP_URL_ROOT=localhost
ALLOWED_ORIGINS=http://localhost:4200,http://localhost:5200

MEDIA_MANAGER_API='http://*************:8000'

AUDIT_HOST="http://************:9200"
ELASTIC_AUDIT_USER="elastic"
ELASTIC_AUDIT_PASSWORD="WKjV6nf1"
AUDIT_INDEX="laravel_auditing_dev"
AUDIT_TYPE="audits"

AUDIT_BASIC_AUTH=true
AUDIT_USE_CERT=false
