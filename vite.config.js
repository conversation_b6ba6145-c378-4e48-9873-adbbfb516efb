import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
const uniqueHash =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
import { viteStaticCopy } from "vite-plugin-static-copy";

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/sass/app.scss',
                'resources/sass/global.scss',
                'resources/sass/components/dashboard-main.scss',
                'resources/sass/components/dashboard.scss',
                'resources/sass/components/header.scss',
                'resources/sass/components/pdf_editor.scss',
                'resources/sass/components/project-info.scss',
                'resources/sass/components/simulation.scss',
                'resources/js/app.js',
                'resources/js/translation.js',
                'resources/js/blog.js',
                'resources/js/project/dashboard.js',
                'resources/js/filament/media-manager.js',
                'resources/css/filament/admin/media-manager.css',
                'resources/js/setting/my-setting.js',
                'resources/js/project/creation.js',
                'resources/js/socket.js',
                'resources/css/filament/dashboard/theme.css',
                'resources/css/filament/dashboard/theme-client.css',
                'resources/css/filament/dashboard/toastify.css',
                'resources/css/filament/admin/theme.css',
                'resources/css/app.css',
                'resources/css/flag-icons.min.css',
                'resources/css/fonts.css',
            ],
            refresh: [
                'resources/views/**',
                'routes/**',
                'app/**',
            ],
        }),
        viteStaticCopy({
            targets: [
                {
                    src: "node_modules/monaco-editor/min/vs",
                    dest: "assets/monaco-editor",
                },
            ],
        }),
    ],
    server: {
        watch: {
            ignored: [
                '**/lang/**',
            ],
        },
    },
    resolve: {
        alias: {
            "@fonts": "/public/fonts",
        },
    },
    // build: {
    //     manifest: true,
    //     outDir: 'public/build',
    //     rollupOptions: {
    //         input: [
    //             'resources/sass/app.scss',
    //             'resources/sass/global.scss',
    //             'resources/js/app.js',
    //             'resources/js/translation.js',
    //             'resources/js/project/dashboard.js',
    //             'resources/js/setting/my-setting.js',
    //             'resources/js/project/creation.js',
    //             'resources/js/blog.js',
    //             'resources/css/filament/dashboard/theme.css',
    //             'resources/css/filament/admin/theme.css',
    //             'resources/css/app.css',
    //             'resources/css/flag-icons.min.css',
    //             'resources/css/fonts.css',
    //         ],
    //         output: {
    //             entryFileNames: `assets/[name]-${uniqueHash}.js`,
    //             chunkFileNames: `assets/[name]-${uniqueHash}.js`,
    //             assetFileNames: `assets/[name]-${uniqueHash}.[ext]`,
    //         },
    //     },
    // },
});
