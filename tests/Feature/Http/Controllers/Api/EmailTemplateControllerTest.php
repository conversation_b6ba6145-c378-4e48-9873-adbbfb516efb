<?php

namespace Tests\Feature\Http\Controllers\Api;

use App\Constants\TemplateStatus;
use App\Models\EmailTemplate;
use Tests\Feature\FeatureTest;

class EmailTemplateControllerTest extends FeatureTest
{
    public function test_can_create_email_template(): void
    {
        $user = $this->createUser();
        $this->actingAs($user, 'sanctum');

        $payload = [
            'key' => 'welcome-email',
            'name' => 'Welcome Email',
            'description' => 'Welcome template.',
            'locale' => 'en',
            'status' => TemplateStatus::ACTIVE->value,
            'head' => '<head>Header</head>',
            'content' => '<p>Hello</p>',
            'foot' => '<footer>Bye</footer>',
            'style' => 'body { color: #000; }',
            'script' => 'console.log("test");',
        ];

        $response = $this->postJson(route('email-templates.store'), $payload);

        $response
            ->assertCreated()
            ->assertJsonPath('data.key', $payload['key'])
            ->assertJsonPath('data.created_by', $user->id);

        $this->assertDatabaseHas('email_templates', [
            'key' => $payload['key'],
            'created_by' => $user->id,
        ]);
    }

    public function test_can_list_email_templates(): void
    {
        $user = $this->createUser();
        $this->actingAs($user, 'sanctum');

        $templates = EmailTemplate::factory()->count(2)->create(['created_by' => $user->id]);

        $response = $this->getJson(route('email-templates.index'));

        $response->assertOk();

        $ids = collect($response->json('data'))->pluck('id');

        $templates->each(function (EmailTemplate $template) use ($ids) {
            $this->assertTrue($ids->contains($template->id));
        });
    }

    public function test_can_show_email_template(): void
    {
        $user = $this->createUser();
        $this->actingAs($user, 'sanctum');

        $template = EmailTemplate::factory()->create(['created_by' => $user->id]);

        $response = $this->getJson(route('email-templates.show', $template));

        $response
            ->assertOk()
            ->assertJsonPath('data.id', $template->id);
    }

    public function test_can_update_email_template(): void
    {
        $user = $this->createUser();
        $this->actingAs($user, 'sanctum');

        $template = EmailTemplate::factory()->create(['created_by' => $user->id]);

        $payload = [
            'name' => 'Updated Name',
            'status' => TemplateStatus::ACTIVE->value,
        ];

        $response = $this->patchJson(route('email-templates.update', $template), $payload);

        $response
            ->assertOk()
            ->assertJsonPath('data.name', $payload['name'])
            ->assertJsonPath('data.status', $payload['status']);

        $this->assertDatabaseHas('email_templates', [
            'id' => $template->id,
            'name' => $payload['name'],
            'status' => $payload['status'],
        ]);
    }

    public function test_can_delete_email_template(): void
    {
        $user = $this->createUser();
        $this->actingAs($user, 'sanctum');

        $template = EmailTemplate::factory()->create(['created_by' => $user->id]);

        $response = $this->deleteJson(route('email-templates.destroy', $template));

        $response
            ->assertOk()
            ->assertJsonPath('success', true);

        $this->assertSoftDeleted('email_templates', [
            'id' => $template->id,
        ]);
    }

    public function test_can_render_email_template(): void
    {
        $user = $this->createUser();
        $this->actingAs($user, 'sanctum');

        $template = EmailTemplate::factory()->create([
            'key' => 'welcome-email-001',
            'head' => '<head><title>{{email_subject}}</title></head>',
            'content' => '<h1>{{title}}</h1><p>Hello {{first_name}}, welcome on {{app_name}}!</p>',
            'foot' => '<footer>{{signature}}</footer>',
            'style' => 'h1 { color: {{color}}; }',
            'script' => "console.log('{{title}}');",
            'created_by' => $user->id,
        ]);

        $payload = [
            'title' => 'Welcome Email',
            'first_name' => 'Angela',
            'email_subject' => 'Welcome to Saasykit',
            'signature' => 'The Saasykit Team',
            'color' => '#2d3748',
        ];

        $response = $this->postJson(route('email-templates.render'), [
            'key' => $template->key,
            'payload' => $payload,
        ]);

        $response
            ->assertOk()
            ->assertJsonPath('success', true)
            ->assertJsonPath('data.head', '<head><title>Welcome to Saasykit</title></head>')
            ->assertJsonPath('data.content', '<h1>Welcome Email</h1><p>Hello Angela, welcome on ' . config('app.name') . '!</p>')
            ->assertJsonPath('data.foot', '<footer>The Saasykit Team</footer>')
            ->assertJsonPath('data.style', 'h1 { color: #2d3748; }')
            ->assertJsonPath('data.script', "console.log('Welcome Email');")
            ->assertJson(fn (array $json) => str_contains($json['data']['html'], '<h1>Welcome Email</h1>'));
    }
}
