<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use App\Models\Country;
use App\Services\IpLocationService;

class ImportIpBlocksCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-ip-blocks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import IP blocks from GitHub';

    /**
     * Execute the console command.
     */
     public function handle(): int
    {
        $repoUrl = "https://api.github.com/repos/herrbischoff/country-ip-blocks/contents/ipv4";

        $response = Http::withHeaders([
            'Accept' => 'application/vnd.github.v3+json',
        ])->get($repoUrl);

        if (!$response->ok()) {
            $this->error("Failed to fetch list of CIDR files from GitHub");
            return self::FAILURE;
        }

        $files = $response->json();

        DB::table('ip_blocks')->truncate();

        foreach ($files as $file) {
            if (!str_ends_with($file['name'], '.cidr')) {
                continue;
            }

            $countryCode = str_replace('.cidr', '', $file['name']);
            $country = Country::where('code_alpha_2', $countryCode)->first();

            if (!$country) {
                $this->warn("Country with code {$countryCode} not found in database. Skipping.");
                continue;
            }

            $this->info("Importing CIDR blocks for country: {$country->name} ({$countryCode})");

            $cidrResponse = Http::get($file['download_url']);
            if (!$cidrResponse->ok()) {
                $this->warn("Failed to fetch {$file['name']}");
                continue;
            }

            $cidrList = explode("\n", trim($cidrResponse->body()));
            $insertData = [];
            $now = now();

            foreach ($cidrList as $cidr) {
                if (!empty($cidr)) {
                    $insertData[] = [
                        'country_id' => $country->id,
                        'cidr'       => $cidr,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }
            }

            if (!empty($insertData)) {
               
                foreach (array_chunk($insertData, 500) as $chunk) {
                    DB::table('ip_blocks')->upsert(
                        $chunk,
                        ['country_id', 'cidr'], 
                        ['updated_at']           
                    );
                }

                $this->info("Processed ".count($insertData)." CIDR blocks for {$country->name}");
            } else {
                $this->warn("No CIDR blocks found for {$country->name}");
            }
        }

        app(IpLocationService::class)->refreshIpBlocksCache();

        $this->info("All CIDR files have been processed successfully!");
        return self::SUCCESS;
    }

}
