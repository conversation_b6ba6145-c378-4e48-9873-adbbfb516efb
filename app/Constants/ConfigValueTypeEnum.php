<?php

namespace App\Constants;

enum ConfigValueTypeEnum: string
{
    case HTML = 'html';
    case STRING = 'string';
    case JSON = 'json';
    case BOOLEAN = 'boolean';

    public static function toArray(): array
    {
        return array_reduce(self::cases(), function ($carry, $case) {
            $carry[$case->value] = ucfirst($case->value);
            return $carry;
        }, []);
    }
}
