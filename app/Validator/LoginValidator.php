<?php

namespace App\Validator;

use App\Support\TwoFactor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LoginValidator
{
    public function validateRequest(Request $request)
    {
        return $request->validate(
            $this->getValidationRules(
                $request->all()
            ));
    }

    public function validate(array $fields)
    {
        return Validator::make($fields, $this->getValidationRules($fields));
    }

    private function getValidationRules(array $fields): array
    {
        $rules = [];

        if (! TwoFactor::isAvailable() || ! isset($fields['2fa_code'])) {
            $rules = [
                'email' => 'required|string',
                'password' => 'required|string',
            ];
        }

        if (config('app.recaptcha_enabled') && (! TwoFactor::isAvailable() || ! isset($fields['2fa_code']))) { // avoid recaptcha when 2FA handles the request
            $rules[recaptchaFieldName()] = recaptchaRuleName();
        }

        return $rules;
    }
}
