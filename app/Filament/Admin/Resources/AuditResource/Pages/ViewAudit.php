<?php

namespace App\Filament\Admin\Resources\AuditResource\Pages;

use App\Filament\Admin\Resources\AuditResource;
use App\Models\Audit;
use App\Models\EsAudit;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\ArrayEntry;
use Filament\Infolists\Components\KeyValueEntry;

class ViewAudit extends ViewRecord
{
    protected static string $resource = AuditResource::class;

    protected static function normalizeAuditValues($value): array
    {
        if (is_array($value)) {
            return $value;
        }

        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }

        if (is_object($value)) {
            return json_decode(json_encode($value), true) ?? [];
        }

        return [];
    }


    public function infolist(Infolist $infolist): Infolist
    {
        /** @var EsAudit $record */
        $record = $this->record;
        $record->loadMissing(['user']);

        $old = self::normalizeAuditValues($record->old_values ?? $record->old_values_json ?? []);
        $new = self::normalizeAuditValues($record->new_values ?? $record->new_values_json ?? []);

        $changes    = self::diffAssocRecursive($old, $new);
        $hasChanges = ! empty($changes);
        $singleData = $hasChanges ? [] : (!empty($new) ? $new : $old);

        $schema = [
            Infolists\Components\Section::make('Audit Information')
                ->columns(12)
                ->compact()
                ->schema([
                    Infolists\Components\TextEntry::make('event')
                        ->label('Event')
                        ->badge()
                        ->color(fn ($state) => match ($state) {
                            'created'  => 'success',
                            'updated'  => 'warning',
                            'deleted'  => 'danger',
                            'restored' => 'info',
                            default    => 'gray',
                        })
                        ->columnSpan(3),

                    Infolists\Components\TextEntry::make('created_at')
                        ->label('Date')
                        ->icon('heroicon-m-clock')
                        ->dateTime()
                        ->columnSpan(3),

                    Infolists\Components\TextEntry::make('user_display')
                        ->label('User')
                        ->state(function (EsAudit $r) {
                            if ($r->relationLoaded('user') && $r->user) {
                                $full = trim(($r->user->first_name ?? '').' '.($r->user->last_name ?? ''));
                                $full = $full !== '' ? $full : ($r->user->name ?? "User #{$r->user->id}");
                                return $full;
                            }

                            if (!empty($r->user_name)) {
                                return $r->user_name;
                            }

                            return $r->user_type
                                ? class_basename($r->user_type).' #'.$r->user_id
                                : '—';
                        })
                        ->helperText(fn (EsAudit $r) =>
                            $r->user->email
                                ?? $r->user_email
                                ?? null
                        )
                        ->columnSpan(3),

                     Infolists\Components\TextEntry::make('audited_model')
                        ->label('Model')
                        ->icon('heroicon-m-cube')
                        ->state(function (EsAudit $r) {
                            $type = $r->auditable_type
                                ?? $r->model_type
                                ?? $r->subject_type
                                ?? null;

                            $id = $r->auditable_id
                                ?? $r->model_id
                                ?? $r->subject_id
                                ?? null;

                            $name = $type ? class_basename($type) : '—';

                            return $id ? "{$name} #ID : {$id}" : $name;
                        })
                        ->columnSpan(3),

                    Infolists\Components\TextEntry::make('url')
                        ->label('URL')
                        ->icon('heroicon-m-link')
                        ->copyable()
                        ->formatStateUsing(fn (?string $state) => $state ? str($state)->limit(80) : '—')
                        ->helperText(fn (EsAudit $r) => collect([
                                $r->ip_address ? "IP: {$r->ip_address}" : null, 
                            ])
                            ->filter()
                            ->implode(' | ')
                        )
                        ->columnSpan(12),
                        
                    Infolists\Components\TextEntry::make('tags')
                        ->label('Tags')
                        ->state(function (EsAudit $r) {
                            return collect($r->tags ?? [])
                                ->when(is_string($r->tags ?? null), fn ($c) => collect(explode(',', $r->tags)))
                                ->map(fn ($v) => trim((string) $v))
                                ->filter()
                                ->unique()
                                ->values()
                                ->all();
                        })
                        ->columnSpan(12),
                 
                ]),

                ...(empty($singleData) ? [
                    Infolists\Components\Grid::make(['default' => 1, 'md' => 2])->schema([
                        Infolists\Components\Section::make('Old Data')
                            ->collapsible()
                            ->collapsed()
                            ->icon('heroicon-m-archive-box')
                            ->description('Full snapshot of data before changes')
                            ->schema([
                                Infolists\Components\KeyValueEntry::make('old_values')
                                    ->label('')
                                    ->keyLabel('Field')
                                    ->valueLabel('Value')
                                    ->state(fn () => self::flattenForDisplay($old)),   
                            ]),

                        Infolists\Components\Section::make('New Data')
                            ->collapsible()
                            ->collapsed()
                            ->icon('heroicon-m-sparkles')
                            ->description('Full snapshot of data after changes')
                            ->schema([
                                Infolists\Components\KeyValueEntry::make('new_values')
                                    ->label('')
                                    ->keyLabel('Field')
                                    ->valueLabel('Value')
                                    ->state(fn () => self::flattenForDisplay($new)),   
                            ]),
                    ]),
                ] : []),

                ...((! $hasChanges && ! empty($singleData)) ? [
                    Infolists\Components\Section::make('Data')
                        ->collapsible()
                        ->icon('heroicon-m-document-text')
                        ->description('Complete data for this audit event')
                        ->schema([
                            Infolists\Components\KeyValueEntry::make('single_data')
                                ->label('')
                                ->keyLabel('Field')
                                ->valueLabel('Value')
                                ->state(fn () => self::flattenForDisplay($singleData)), 
                        ]),
                ] : []),

            // ...(empty($singleData) ? [
            //     Infolists\Components\Grid::make(['default' => 1, 'md' => 2])->schema([
            //         Infolists\Components\Section::make('Old Data')
            //             ->collapsible()
            //             ->collapsed()
            //             ->icon('heroicon-m-archive-box')
            //             ->description('Full snapshot of data before changes')
            //             ->schema([
            //                 Infolists\Components\KeyValueEntry::make('old_values')
            //                     ->label('')
            //                     ->keyLabel('Field')
            //                     ->valueLabel('Value')
            //                     ->state(fn () => $old),      
            //             ]),

            //         Infolists\Components\Section::make('New Data')
            //             ->collapsible()
            //             ->collapsed()
            //             ->icon('heroicon-m-sparkles')
            //             ->description('Full snapshot of data after changes')
            //             ->schema([
            //                 Infolists\Components\KeyValueEntry::make('new_values')
            //                     ->label('')
            //                     ->keyLabel('Field')
            //                     ->valueLabel('Value')
            //                     ->state(fn () => $new),       
            //             ]),
            //     ]),
            // ] : []),

            // ...((! $hasChanges && ! empty($singleData)) ? [
            //     Infolists\Components\Section::make('Data')
            //         ->collapsible()
            //         ->icon('heroicon-m-document-text')
            //         ->description('Complete data for this audit event')
            //         ->schema([
            //             Infolists\Components\KeyValueEntry::make('single_data')
            //                 ->label('')
            //                 ->keyLabel('Field')
            //                 ->valueLabel('Value')
            //                 ->state(fn () => $singleData),
            //         ]),
            // ] : []),
        ];

        return $infolist->schema($schema);
    }

    protected static function isAssoc(array $arr): bool
    {
        if ($arr === []) return false;
        return array_keys($arr) !== range(0, count($arr) - 1);
    }

    protected static function flattenForDisplay(array $data, string $prefix = ''): array
    {
        $out = [];

        foreach ($data as $k => $v) {
            $path = $prefix === '' ? (string) $k : $prefix . '.' . $k;

            if (is_object($v)) {
                $v = json_decode(json_encode($v), true) ?? [];
            }

            if (is_array($v)) {
                if (self::isAssoc($v)) {
                    $out += self::flattenForDisplay($v, $path);
                } else {
                    foreach ($v as $i => $iv) {
                        if (is_array($iv) || is_object($iv)) {
                            $iv = is_object($iv) ? (json_decode(json_encode($iv), true) ?? []) : $iv;
                            $out += self::flattenForDisplay($iv, $path . '.' . $i);
                        } else {
                            $out[$path . '.' . $i] = self::encodeScalar($iv);
                        }
                    }
                }
            } else {
                $out[$path] = self::encodeScalar($v);
            }
        }

        return $out;
    }


    public static function diffAssocRecursive(array $old, array $new, string $prefix = ''): array
    {
        $keys = array_unique(array_merge(array_keys($old), array_keys($new)));
        $rows = [];

        foreach ($keys as $key) {
            $path = ltrim($prefix . '.' . $key, '.');

            $ov = $old[$key] ?? null;
            $nv = $new[$key] ?? null;

            $ovIsArr = is_array($ov);
            $nvIsArr = is_array($nv);

            if ($ovIsArr || $nvIsArr) {
                $rows = array_merge($rows, self::diffAssocRecursive(
                    $ovIsArr ? $ov : [],
                    $nvIsArr ? $nv : [],
                    $path
                ));
            } else {
                if ($ov !== $nv) {
                    $rows[] = [
                        'path' => $path,
                        'old'  => self::encodeScalar($ov),
                        'new'  => self::encodeScalar($nv),
                    ];
                }
            }
        }

        return $rows;
    }

    protected static function encodeScalar($value): string
    {
        if (is_bool($value)) return $value ? 'true' : 'false';
        if ($value === null) return 'null';
        if (is_scalar($value)) return (string) $value;
        return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
}