<?php

namespace App\Filament\Admin\Resources\CmsResource\Pages;

use App\Filament\Admin\Resources\CmsResource;
use App\Services\CmsService;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;

class ListCms extends ListRecords
{
    protected static string $resource = CmsResource::class;

    // protected static string $view = 'filament.admin.pages.cms.cms-create';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.cms'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'cms',
                ])),
        ];
    }
}
