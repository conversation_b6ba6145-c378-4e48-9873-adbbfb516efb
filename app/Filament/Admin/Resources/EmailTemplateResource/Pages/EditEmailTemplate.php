<?php

namespace App\Filament\Admin\Resources\EmailTemplateResource\Pages;

use App\Constants\TemplateStatus;
use App\Filament\Admin\Forms\Components\LanguageMultiPicker;
use App\Filament\Admin\Resources\EmailTemplateResource;
use App\Models\EmailTemplate;
use App\Models\Language;
use App\Services\TranslationApiService;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms;
use Filament\Forms\Get;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Throwable;

class EditEmailTemplate extends EditRecord
{
    protected static string $resource = EmailTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('auto_translate')
                ->label('Auto-translate')
                ->icon('heroicon-o-language')
                ->color('primary')
                ->modalHeading('Generate translations')
                ->modalDescription('Select the languages that should receive updated content for this template.')
                ->modalSubmitActionLabel('Translate')
                ->modalWidth('4xl')
                ->form(fn () => $this->getAutoTranslateFormSchema())
                ->action(fn (array $data) => $this->handleAutoTranslateRequest($data)),
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $key = $data['key'] ?? $record->key;
        $languageId = (int) ($data['language_id'] ?? $record->language_id);
        $editingTemplateId = $data['editing_template_id'] ?? null;

        $template = null;

        if ($editingTemplateId) {
            $template = EmailTemplate::query()->find($editingTemplateId);
        }

        if ($template === null || (int) $template->language_id !== $languageId || $template->key !== $key) {
            $template = EmailTemplate::findByKeyAndLanguage($key, $languageId);
        }

        if ($template === null) {
            $template = EmailTemplate::make();
            $template->language_id = $languageId;
            $template->created_by = $record->created_by ?? Auth::id();
        }

        $language = Language::query()->firstWhere('id', $languageId)?->toArray();

        if ($language !== null) {
            $isoCode = strtolower((string) ($language['languageISO2'] ?? ''));

            if ($isoCode !== '') {
                $template->locale = $isoCode;
            }
        }

        $baseTemplate = $this->resolveStrictBaseTemplate($key) ?? $template;
        $baseLanguageId = $baseTemplate->language_id;
        $isBaseRecord = (int) $template->language_id === (int) $baseLanguageId;

        if ($isBaseRecord) {
            $template->key = $key;
            $template->name = $data['name'];
            $template->description = $data['description'] ?? null;
            $template->status = $data['status'] ?? ($template->status instanceof TemplateStatus ? $template->status->value : ($template->status ?? TemplateStatus::DRAFT->value));
            $template->is_shared = (bool) ($data['is_shared'] ?? false);
            $template->head = $data['head'] ?? null;
            $template->content = $data['content'] ?? null;
            $template->foot = $data['foot'] ?? null;
            $template->style = $data['style'] ?? null;
            $template->script = $data['script'] ?? null;
            $template->metadata = $data['metadata'] ?? null;
            $template->id_row = null;
        } else {
            $template->key = null;
            if (! $template->exists) {
                $template->name = $data['name'] ?? $baseTemplate->name;
                $template->description = $data['description'] ?? $baseTemplate->description;
                $template->is_shared = (bool) $baseTemplate->is_shared;
            }

            $template->style = null;
            $template->script = null;
            $template->metadata = null;
            $template->status = null;
            $template->id_row = $baseTemplate->id ?? null;

            $template->name = $data['name'] ?? $template->name;
            $template->description = $data['description'] ?? $template->description;
            $template->head = $data['head'] ?? $template->head;
            $template->content = $data['content'] ?? $template->content;
            $template->foot = $data['foot'] ?? $template->foot;
        }

        $template->save();

        $this->record = $template->fresh();

        $this->form->fill(array_merge(
            $data,
            [
                'editing_template_id' => $this->record->id,
                'language_id' => $this->record->language_id,
                'status' => $isBaseRecord
                    ? ($this->record->status instanceof TemplateStatus ? $this->record->status->value : $this->record->status)
                    : TemplateStatus::DRAFT->value,
                'is_shared' => (bool) $this->record->is_shared,
                'id_row' => $this->record->id_row,
            ]
        ));

        return $this->record;
    }

    public function handleAutoTranslateRequest(array $data): void
    {
        $baseTemplate = $this->resolveBaseTemplate();

        if ($baseTemplate === null) {
            Notification::make()
                ->danger()
                ->title('Base template missing')
                ->body('Create the English template before running auto-translation.')
                ->send();

            return;
        }

        $translateAll = (bool) ($data['translate_all'] ?? false);

        $languageIds = collect(Arr::wrap($data['languages'] ?? []))
            ->map(fn ($id) => (int) $id)
            ->filter(fn ($id) => $id > 0)
            ->unique()
            ->values()
            ->all();

        $this->processAutoTranslation($baseTemplate, $languageIds, $translateAll);
    }

    protected function processAutoTranslation(EmailTemplate $baseTemplate, array $languageIds, bool $translateAll): void
    {
        try {
            $languages = Language::query()->get();
            $baseLanguage = $languages->firstWhere('id', $baseTemplate->language_id);

            if ($baseLanguage === null || empty($baseLanguage['languageISO2'])) {
                Notification::make()
                    ->danger()
                    ->title('Language lookup failed')
                    ->body('Unable to resolve the base language ISO code.')
                    ->send();

                return;
            }

            $targetLanguages = $translateAll || empty($languageIds)
                ? $languages
                : $languages->filter(fn ($language) => in_array((int) $language['id'], $languageIds, true));

            $targetLanguages = $targetLanguages
                ->filter(fn ($language) => (int) $language['id'] !== (int) $baseTemplate->language_id)
                ->values();

            if ($targetLanguages->isEmpty()) {
                Notification::make()
                    ->warning()
                    ->title('No target languages selected')
                    ->body('Choose at least one language to translate into.')
                    ->send();

                return;
            }

            $translationService = app(TranslationApiService::class);
            $baseFields = $this->extractTranslatableFields($baseTemplate);
            $baseIso = strtolower((string) $baseLanguage['languageISO2']);

            $fieldsNeedingTranslation = Arr::only($baseFields, ['name', 'description', 'head', 'content', 'foot']);

            $targetIsoList = $targetLanguages
                ->map(fn ($language) => strtolower((string) ($language['languageISO2'] ?? '')))
                ->filter(fn ($iso) => $iso !== '')
                ->values()
                ->all();

            if (empty($targetIsoList)) {
                Notification::make()
                    ->warning()
                    ->title('No translatable languages')
                    ->body('The selected languages do not have ISO codes configured.')
                    ->send();

                return;
            }

            $translationsByField = $translationService->translateEmailTemplateFields(
                $fieldsNeedingTranslation,
                $targetIsoList,
                $baseIso
            );

            $translatedLanguages = [];

            foreach ($targetLanguages as $language) {
                $targetIso = strtolower((string) ($language['languageISO2'] ?? ''));

                if ($targetIso === '') {
                    continue;
                }

                $translated = $baseFields;

                foreach ($fieldsNeedingTranslation as $field => $value) {
                    $translatedValue = $translationsByField[$field][$targetIso] ?? null;

                    if ($translatedValue === null || $translatedValue === '') {
                        continue;
                    }

                    $translated[$field] = $translatedValue;
                }

                $targetTemplate = EmailTemplate::query()
                    ->withTrashed()
                    ->forKeyAndLanguage($baseTemplate->key, (int) $language['id'])
                    ->first();

                if ($targetTemplate === null) {
                    $targetTemplate = EmailTemplate::make();
                    $targetTemplate->language_id = (int) $language['id'];
                    $targetTemplate->created_by = $baseTemplate->created_by ?? Auth::id();
                } elseif ($targetTemplate->trashed()) {
                    $targetTemplate->restore();
                }

                $targetTemplate->name = $translated['name'] ?? $baseTemplate->name;
                $targetTemplate->description = $translated['description'] ?? $baseTemplate->description;
                $targetTemplate->head = $translated['head'] ?? $baseTemplate->head;
                $targetTemplate->content = $translated['content'] ?? $baseTemplate->content;
                $targetTemplate->foot = $translated['foot'] ?? $baseTemplate->foot;
                $targetTemplate->style = null;
                $targetTemplate->script = null;
                $targetTemplate->metadata = null;
                $targetTemplate->status = null;
                $targetTemplate->is_shared = (bool) $baseTemplate->is_shared;

                $localeIso = strtolower((string) ($language['languageISO2'] ?? ''));
                if ($localeIso !== '') {
                    $targetTemplate->locale = $localeIso;
                }

                $targetTemplate->key = null;
                $targetTemplate->id_row = $baseTemplate->id;

                $targetTemplate->save();

                $translatedLanguages[] = trim(sprintf(
                    '%s%s',
                    $language['languageFull'] ?? ('ID '.$language['id']),
                    $language['languageISO2'] ? ' ('.strtoupper((string) $language['languageISO2']).')' : ''
                ));
            }

            if (empty($translatedLanguages)) {
                Notification::make()
                    ->warning()
                    ->title('No translations generated')
                    ->body('Nothing was translated. Verify that the selected languages are configured correctly.')
                    ->send();

                return;
            }

            Notification::make()
                ->success()
                ->title('Translations completed')
                ->send();

            $this->record = $this->record->fresh();
            $this->fillForm();
        } catch (Throwable $throwable) {
            Log::error('Email template auto-translation failed', [
                'template_id' => $this->record->id,
                'exception' => $throwable,
            ]);

            Notification::make()
                ->danger()
                ->title('Translation failed')
                ->body('Unable to complete auto-translation. Please try again later.')
                ->send();
        }
    }

    protected function getAutoTranslateFormSchema(): array
    {
        $baseTemplate = $this->resolveBaseTemplate();

        if ($baseTemplate === null) {
            return [
                Forms\Components\Placeholder::make('missing_base_template')
                    ->label('Auto-translate')
                    ->content('Create an English base template before running auto-translation.'),
            ];
        }

        $options = $this->getAutoTranslateLanguageOptions($baseTemplate);

        if (empty($options)) {
            return [
                Forms\Components\Placeholder::make('no_languages')
                    ->label('Auto-translate')
                    ->content('No other languages are available for translation yet.'),
            ];
        }

        $defaultSelection = count($options) === 1 ? [array_key_first($options)] : [];

        return [
            Forms\Components\Toggle::make('translate_all')
                ->label('Translate into all available languages')
                ->default(true)
                ->live()
                ->columnSpanFull(),
            LanguageMultiPicker::make('languages')
                ->label(count($options) === 1 ? 'Target language' : 'Target languages')
                ->options($options)
                ->placeholder('Select languages')
                ->searchPrompt('Search languages...')
                ->visibleLimit(12)
                ->default($defaultSelection)
                ->hidden(fn (Get $get) => (bool) $get('translate_all'))
                ->required(fn (Get $get) => ! (bool) $get('translate_all'))
                ->helperText('Uncheck the toggle above to select specific languages, or use the actions above to check them all.')
                ->columnSpanFull(),
        ];
    }

    protected function getAutoTranslateLanguageOptions(EmailTemplate $baseTemplate): array
    {
        $languages = Language::query()
            ->orderBy('languageFull')
            ->get();

        $baseLanguageId = (int) $baseTemplate->language_id;
        $options = [];

        foreach ($languages as $language) {
            if ((int) $language->id === $baseLanguageId) {
                continue;
            }

            $name = (string) $language->languageFull;
            $native = $language->languageFullNative ? (string) $language->languageFullNative : null;
            $flagCode = $language->flagCode ?? $language->languageISO2 ?? null;
            $flagCode = $flagCode ? strtolower((string) $flagCode) : null;
            $iso = $language->languageISO2 ? strtoupper((string) $language->languageISO2) : null;

            $options[(int) $language->id] = [
                'name' => $name,
                'native' => $native,
                'iso' => $iso,
                'flag' => $flagCode,
            ];
        }

        return $options;
    }

    private function resolveBaseTemplate(): ?EmailTemplate
    {
        $key = $this->record->key;

        $languages = Language::query()->get();
        $baseLanguage = $languages->firstWhere('isBaseLanguage', true);

        if ($baseLanguage !== null) {
            $template = EmailTemplate::findByKeyAndLanguage($key, (int) $baseLanguage['id']);

            if ($template !== null) {
                return $template;
            }
        }

        if ($this->record->locale === 'en') {
            return $this->record;
        }

        $englishTemplate = EmailTemplate::query()
            ->where('key', $key)
            ->where('locale', 'en')
            ->first();

        return $englishTemplate ?? $this->record;
    }

    private function extractTranslatableFields(EmailTemplate $template): array
    {
        return [
            'name' => $template->name,
            'description' => $template->description,
            'head' => $template->head,
            'content' => $template->content,
            'foot' => $template->foot,
            'style' => $template->style,
            'script' => $template->script,
        ];
    }

    private function resolveStrictBaseTemplate(string $key): ?EmailTemplate
    {
        $languages = Language::query()->get();
        $baseLanguage = $languages->firstWhere('isBaseLanguage', true);

        if ($baseLanguage !== null) {
            $template = EmailTemplate::findByKeyAndLanguage($key, (int) $baseLanguage['id']);

            if ($template !== null) {
                return $template;
            }
        }

        return EmailTemplate::query()
            ->where('key', $key)
            ->where('locale', 'en')
            ->first();
    }
}
