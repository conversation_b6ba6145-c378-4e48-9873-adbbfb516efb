<?php

namespace App\Filament\Admin\Resources\Concerns;

use App\Models\EsAudit;
use App\Models\Audit;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class AuditsRelationManager extends RelationManager
{
    protected static string $relationship = 'audits';
    protected static ?string $title = 'Audits';

    public function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->recordTitleAttribute('id')
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('event')
                    ->badge()
                    ->colors([
                        'success' => 'created',
                        'warning' => 'updated',
                        'danger'  => 'deleted',
                        'info'    => 'restored',
                    ])
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->description(fn (EsAudit $r) => $r->user?->email)
                    ->toggleable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('auditable_type')
                    ->label('Model')
                    ->formatStateUsing(fn ($state) => $state ? class_basename($state) : null)
                    ->description(fn (EsAudit $r) => $r->auditable ? ('ID #'.$r->auditable->getKey()) : '—')
                    ->toggleable(),

                Tables\Columns\TagsColumn::make('tags')
                    ->limit(3)
                    ->state(function (EsAudit $r) {
                        return collect($r->tags ?? [])
                            ->flatMap(fn ($t) => is_string($t) ? explode(',', $t) : [$t])
                            ->map(fn ($v) => trim((string) $v))
                            ->filter()
                            ->unique()
                            ->values()
                            ->all();
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('event')->options([
                    'created'  => 'Created',
                    'updated'  => 'Updated',
                    'deleted'  => 'Deleted',
                    'restored' => 'Restored',
                ]),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url(fn (Audit $r) => route('filament.admin.resources.audits.view', $r)),
                    // ->openUrlInNewTab(),
            ])
            ->emptyStateHeading('No audits yet');
    }
}
