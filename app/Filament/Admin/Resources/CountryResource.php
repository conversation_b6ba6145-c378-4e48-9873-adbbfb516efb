<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CountryResource\Pages;
use App\Filament\Admin\Resources\CountryResource\RelationManagers;
use App\Models\Country;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Arr;
use App\Services\TranslationApiService;

class CountryResource extends Resource
{
    protected static ?string $model = Country::class;


    public static function getModelLabel(): string
    {
        return t('core.country.title');
    }

    public static function getNavigationGroup(): ?string
    {
        return t('core.locales');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(1)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(t('core.country.name'))
                            ->required(),
                Forms\Components\Select::make('region_id')
                    ->label(t('core.region.title'))
                    ->relationship('region', 'name')
                    ->searchable()
                    ->preload()
                    ->nullable(),
                Forms\Components\Select::make('base_languages')
                    ->label('Base languages')
                    ->multiple()
                    ->allowHtml()
                    ->options(self::getLanguageOptions())
                    ->preload()
                    ->searchable()
                    ->reactive()
                    ->helperText('Select the base languages used in this country.'),
                Forms\Components\Select::make('default_language')
                    ->label('Default language')
                    ->allowHtml()
                    ->options(fn (Get $get) => \Illuminate\Support\Arr::only(self::getLanguageOptions(), (array) ($get('base_languages') ?? [])))
                    ->preload()
                    ->searchable()
                    ->placeholder('Select base languages first')
                    ->disabled(fn (Get $get) => empty($get('base_languages')))
                    ->helperText('Pick the default language from the selected base languages.')
                    ->rule(function (Get $get) {
                        return function (string $attribute, $value, $fail) use ($get) {
                            $langs = (array) ($get('base_languages') ?? []);
                            if (filled($value) && ! in_array($value, $langs, true)) {
                                $fail('The default language must be one of the base languages.');
                            }
                        };
                    }),
                Forms\Components\TextInput::make('normalized_name')
                    ->label(t('core.country.normalized_name'))
                    ->required(),
                        Forms\Components\TextInput::make('code_alpha_2')
                            ->label(t('core.country.code_alpha_2'))
                            ->required(),
                        Forms\Components\TextInput::make('code_alpha_3')
                            ->label(t('core.country.code_alpha_3'))
                            ->required(),
                        Forms\Components\TextInput::make('timezone_offset')
                            ->label(t('core.country.timezone_offset'))
                            ->required(),
                        Forms\Components\Toggle::make('status')
                            ->label(t('core.country.status')),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(t('core.country.name')),
                Tables\Columns\TextColumn::make('region.name')
                    ->label(t('core.region.title'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\ViewColumn::make('base_languages')
                    ->label('Base languages')
                    ->view('filament.tables.columns.base-languages'),
                Tables\Columns\ViewColumn::make('default_language')
                    ->label('Default language')
                    ->view('filament.tables.columns.base-languages'),
                Tables\Columns\TextColumn::make('normalized_name')
                    ->label(t('core.country.normalized_name')),
                Tables\Columns\TextColumn::make('code_alpha_2')
                    ->label(t('core.country.code_alpha_2'))
                    ->limit(50),
                Tables\Columns\TextColumn::make('code_alpha_3')
                    ->label(t('core.country.code_alpha_3'))
                    ->limit(50),
                Tables\Columns\TextColumn::make('timezone_offset')
                    ->label(t('core.country.timezone_offset'))
                    ->limit(50),
                Tables\Columns\IconColumn::make('status')->boolean()
                    ->label(t('core.country.status')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('region_id')
                    ->label(t('core.region.title'))
                    ->relationship('region', 'name')
                    ->preload()
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(t('core.action.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(t('core.action.delete')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label(t('core.action.bulk_delete')),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCountries::route('/'),
        ];
    }

    public static function getLanguageOptions(): array
    {
        $languageOptions = [];

        try {
            $translationService = app(TranslationApiService::class);
            $res = $translationService->getAllLanguages(['limit' => 200]);

            $languages = Arr::get($res, 'data', []);

            $languageOptions = collect($languages)->mapWithKeys(function ($lang) {
                $languageFullNative = htmlspecialchars($lang['languageFullNative'] ?? '');
                $iso2 = strtoupper($lang['languageISO2'] ?? '');
                $flagCode = htmlspecialchars($lang['flagCode'] ?? '');

                if (!$iso2 || !$languageFullNative) {
                    return [];
                }

                $fullName = sprintf('%s (%s)', $languageFullNative, $iso2);
                $labelHtml = $flagCode
                    ? "<span class='flag-icon flag-icon-" . strtolower($flagCode) . " rounded me-2'></span>$fullName"
                    : $fullName;

                return [$iso2 => $labelHtml];
            })->toArray();
        } catch (\Throwable $e) {
            report($e);
        }

        return $languageOptions;
    }
}
