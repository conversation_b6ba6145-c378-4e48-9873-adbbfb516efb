<?php
namespace App\Filament\Admin\Resources\TranslationInterfaceResource\Pages;

use App\Filament\Admin\Resources\TranslationInterfaceResource;
use Filament\Actions;
use Filament\Resources\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;

// Change from ListRecords to Page

class ListTranslationInterfaces extends Page// Extend Page

{
    protected static string $resource = TranslationInterfaceResource::class;

    protected static string $view = 'filament.admin.pages.list-translation-interfaces';

    // You might want to keep the "Create" action if it's standard
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()->url(TranslationInterfaceResource::getUrl('create')),
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.translation_interface.page_title'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'translation_interface',
                ])),
        ];
    }

    public function getTitle(): string | Htmlable
    {
        return t('core.translation');
    }
}
