<?php

namespace App\Filament\Admin\Resources\TestimonyContextResource\Pages;

use App\Filament\Admin\Resources\TestimonyContextResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;

class ManageTestimonyContexts extends ManageRecords
{
    protected static string $resource = TestimonyContextResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Create Context'),
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.testimony.contexts'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'testimony_context',
                ])),
        ];
    }
}

