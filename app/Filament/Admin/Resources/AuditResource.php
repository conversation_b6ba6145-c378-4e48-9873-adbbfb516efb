<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\AuditResource\Pages;
use App\Models\EsAudit;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms;
use Illuminate\Database\Eloquent\Builder;
use App\Models\User;

class AuditResource extends Resource
{
    protected static ?string $model = EsAudit::class;

    protected static ?string $navigationIcon   = 'heroicon-o-clipboard-document-list';
    protected static ?int    $navigationSort   = 99;
    protected static ?string $modelLabel       = 'Audit';
    protected static ?string $pluralModelLabel = 'Audits';

    public static function table(Table $table): Table
    {
        return $table
            ->query(fn () => EsAudit::query())
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('event')
                    ->badge()
                    ->colors([
                        'success' => 'created',
                        'warning' => 'updated',
                        'danger'  => 'deleted',
                        'info'    => 'restored',
                    ])
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('auditable_type')
                    ->label('Model')
                    ->formatStateUsing(fn (?string $state) => $state ? class_basename($state) : '-')
                    ->description(fn ($record) => $record->auditable_id ? ('ID #'.$record->auditable_id) : '-')
                    ->sortable()
                    ->toggleable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('user.full_name')
                    ->label('User')
                    ->getStateUsing(fn ($record) =>
                        $record->user
                            ? "{$record->user->first_name} {$record->user->last_name}"
                            : '-'
                    )
                    ->description(fn ($record) => $record->user?->email ?? '-')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),


                Tables\Columns\TagsColumn::make('tags')
                    ->limit(3)
                    ->separator(',') 
                    ->toggleable(),

                Tables\Columns\TextColumn::make('ip_address')
                    ->label('IP')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('url')
                    ->label('URL')
                    ->limit(40)
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->copyable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('event')
                    ->options([
                        'created'  => 'Created',
                        'updated'  => 'Updated',
                        'deleted'  => 'Deleted',
                        'restored' => 'Restored',
                    ]),

                Tables\Filters\SelectFilter::make('auditable_type')
                    ->label('Model')
                    ->options(fn () => EsAudit::auditableTypeOptions()),

                Tables\Filters\Filter::make('date')
                    ->form([
                        Forms\Components\DatePicker::make('from')->label('From'),
                        Forms\Components\DatePicker::make('to')->label('To'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when($data['from'] ?? null, fn ($q, $date) => $q->whereDate('created_at', '>=', $date))
                            ->when($data['to']   ?? null, fn ($q, $date) => $q->whereDate('created_at', '<=', $date));
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url(fn ($record) => static::getUrl('view', ['record' => $record->id])),
            ])
            ->bulkActions([])
            ->emptyStateHeading('No audits yet')
            ->emptyStateDescription('Changes will appear here automatically.');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAudits::route('/'),
            'view'  => Pages\ViewAudit::route('/{record}'),
        ];
    }
}
