<?php
namespace App\Filament\Admin\Resources\LanguageResource\Pages;

use App\Filament\Admin\Resources\LanguageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;

class ListLanguages extends ListRecords
{
    protected static string $resource = LanguageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.language.page_title'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'language',
                ])),
        ];
    }

    public function getTitle(): string | Htmlable
    {
        return t('core.languages');
    }

    // protected function getHeaderWidgets(): array
    // {
    //     return [
    //         LanguageOverview::class,
    //     ];
    // }
}
