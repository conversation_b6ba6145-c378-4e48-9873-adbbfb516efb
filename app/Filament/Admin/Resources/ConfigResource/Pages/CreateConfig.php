<?php

namespace App\Filament\Admin\Resources\ConfigResource\Pages;

use App\Filament\Admin\Resources\ConfigResource;
use App\Models\Config;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateConfig extends CreateRecord
{
    protected static string $resource = ConfigResource::class;

    public function getTitle(): string
    {
        return 'Create Setting';
    }

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        $config = Config::create($this->mutateFormDataBeforeCreate($data));

        Notification::make()
            ->success()
            ->title(__('Config created'))
            ->body(__('The config has been created successfully.'))
            ->send();

        return $config;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if (($data['value_type'] ?? null) === 'boolean') {
            // Ensure boolean saved as '1'/'0'
            $data['value'] = !empty($data['value']) ? '1' : '0';
        }
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
