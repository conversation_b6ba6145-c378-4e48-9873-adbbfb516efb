<?php

namespace App\Filament\Admin\Resources\ConfigResource\Pages;

use App\Filament\Admin\Resources\ConfigResource;
use App\Models\Config;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditConfig extends EditRecord
{
    protected static string $resource = ConfigResource::class;

    public function getTitle(): string
    {
        return 'Edit Setting';
    }

    protected function handleRecordUpdate($record, array $data): \Illuminate\Database\Eloquent\Model
    {
        logger(json_encode($data));
        $record->update($data);

        Notification::make()
            ->success()
            ->title(__('Config updated'))
            ->body(__('The config has been updated successfully.'))
            ->send();

        return $record;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        if (($data['value_type'] ?? null) === 'boolean') {
            // Initialize the toggle correctly from various truthy/falsey representations
            $state = $data['value'] ?? 0;
            $data['value'] = in_array($state, ['1', 1, true, 'true'], true);
        }
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (($data['value_type'] ?? null) === 'boolean') {
            // Ensure false persists even if unchecked (missing or false)
            $data['value'] = !empty($data['value']) ? '1' : '0';
        }
        return $data;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->visible(fn (Config $record): bool => $record->deletable)
                ->requiresConfirmation()
                ->successNotification(
                    Notification::make()
                        ->success()
                        ->title(__('Config deleted'))
                        ->body(__('The config has been deleted successfully.'))
                ),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
