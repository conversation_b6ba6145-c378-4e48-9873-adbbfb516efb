<?php

namespace App\Filament\Admin\Resources\TestimonyResource\Pages;

use App\Filament\Admin\Resources\TestimonyResource;
use App\Filament\Admin\Resources\TestimonyResource\Widgets\TestimonyContextsWidget;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Infolists\Components\Section;
use Filament\Support\Enums\MaxWidth;

class ManageTestimonies extends ManageRecords
{
    protected static string $resource = TestimonyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label(t('core.testimony.create')),
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.testimony.page_title'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'testimony',
                ])),

        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            TestimonyContextsWidget::class,
        ];
    }
}
