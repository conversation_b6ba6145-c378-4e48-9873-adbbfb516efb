<?php

namespace App\Filament\Admin\Resources\DiscountResource\Pages;

use App\Filament\Admin\Resources\DiscountResource;
use App\Filament\ListDefaults;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;

class ListDiscounts extends ListRecords
{
    use ListDefaults;

    protected static string $resource = DiscountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.discount.page_title'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'discount',
                ])),
        ];
    }
}
