<?php

namespace App\Filament\Admin\Resources\OauthLoginProviderResource\Pages;

use App\Filament\Admin\Resources\OauthLoginProviderResource;
use App\Filament\ListDefaults;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;

class ListOauthLoginProviders extends ListRecords
{
    use ListDefaults;

    protected static string $resource = OauthLoginProviderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\CreateAction::make(),
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.oauth_login_provider.page_title'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'oauth_login_provider',
                ])),
        ];
    }
}
