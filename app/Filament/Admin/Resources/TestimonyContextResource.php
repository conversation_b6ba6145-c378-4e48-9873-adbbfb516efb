<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TestimonyContextResource\Pages;
use App\Models\TestimonyContext;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class TestimonyContextResource extends Resource
{
    protected static ?string $model = TestimonyContext::class;

    public static function getNavigationGroup(): ?string
    {
        return t('core.cms');
    }

    public static function getModelLabel(): string
    {
        return 'Context';
    }

    public static function getPluralModelLabel(): string
    {
        return 'Contexts';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(1)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Name')
                            ->required()
                            ->maxLength(255),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(t('core.testimony_context.name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('slug')
                    ->label(t('core.testimony_context.slug'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(t('core.updated_at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTestimonyContexts::route('/'),
        ];
    }
}

