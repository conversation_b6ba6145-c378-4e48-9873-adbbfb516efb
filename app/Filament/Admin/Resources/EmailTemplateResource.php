<?php

namespace App\Filament\Admin\Resources;

use App\Constants\TemplateStatus;
use App\Filament\Admin\Resources\EmailTemplateResource\Pages;
use App\Models\EmailTemplate;
use App\Models\Language;
use App\Forms\Components\CustomMonacoEditor;
use App\Services\EmailTemplateRenderer;
use App\Services\TranslationApiService;
use App\Support\LocaleDirection;
use Filament\Forms;
use Filament\Forms\Get;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use JsonException;
use Illuminate\Validation\Rule;
use Throwable;

class EmailTemplateResource extends Resource
{
    protected static ?string $model = EmailTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\View::make('filament.forms.components.auto-translate-loader')
                    ->dehydrated(false)
                    ->columnSpanFull(),
                Forms\Components\Grid::make(['default' => 12])
                    ->schema([
                        // LEFT: editor panels
                        Forms\Components\Group::make([
                            Forms\Components\Section::make('Template Details')
                                ->schema([
                                    Forms\Components\Hidden::make('editing_template_id')
                                        ->default(fn (?EmailTemplate $record) => $record?->id)
                                        ->dehydrated(false)
                                        ->afterStateHydrated(function (Forms\Components\Hidden $component, ?EmailTemplate $record): void {
                                            $component->state($record?->id);
                                        }),
                                    Forms\Components\Hidden::make('id_row')
                                        ->default(fn (?EmailTemplate $record) => $record?->id_row)
                                        ->dehydrated(false),
                                    Forms\Components\TextInput::make('key')
                                        ->required(fn (Get $get) => $get('id_row') === null)
                                        ->maxLength(255)
                                        ->readOnly(fn (?EmailTemplate $record) => $record?->exists)
                                        ->extraAttributes(fn (?EmailTemplate $record) => $record?->exists
                                            ? ['class' => 'email-template-key-input']
                                            : [])
                                        ->rules([
                                            fn (Get $get) => Rule::unique('email_templates', 'key')
                                                ->ignore($get('editing_template_id'))
                                                ->where(function ($query) use ($get) {
                                                    $languageId = $get('language_id');

                                                    return $languageId !== null
                                                        ? $query->where('language_id', (int) $languageId)
                                                        : $query;
                                                }),
                                        ]),
                                    Forms\Components\TextInput::make('name')
                                        ->required()
                                        ->maxLength(255),
                                    Forms\Components\Select::make('language_id')
                                        ->label('Language')
                                        ->required()
                                        ->searchable()
                                        ->preload()
                                        ->options(fn (): array => static::languageOptions())
                                        ->allowHtml()
                                        ->default(fn (): ?int => static::defaultLanguageId())
                                        ->placeholder(t('core.select_a_language'))
                                        ->live()
                                        ->afterStateUpdated(function ($state, Set $set, Get $get, Forms\Components\Select $component): void {
                                            static::handleLanguageSelection($component, $state, $set, $get);
                                        }),
                                    Forms\Components\Select::make('status')
                                        ->options(static::statusOptions())
                                        ->default(TemplateStatus::DRAFT->value),
                                    Forms\Components\Toggle::make('is_shared')
                                        ->label('Shared')
                                        ->columnSpan(['default' => 2, 'md' => 1])
                                        ->live()
                                        ->afterStateUpdated(fn ($state, Set $set, Get $get) => static::updatePreviewSections($set, $get)),
                                ])
                                ->columns(2),

                            Forms\Components\Section::make('Content Blocks')
                                ->extraAttributes(['class' => 'relative'])
                                ->schema([
                                    Forms\Components\View::make('filament.forms.components.email-template-preview')
                                        ->statePath('preview_sections')
                                        ->afterStateHydrated(function (Forms\Components\View $component, $state, Get $get) {
                                            if (empty($state)) {
                                                $component->state(static::renderEmailSections($get));
                                            }
                                        })
                                        ->live()
                                        ->dehydrated(false),
                                    Forms\Components\Tabs::make('template-blocks')
                                        ->tabs([
                                            Forms\Components\Tabs\Tab::make('Head')
                                                ->icon('heroicon-o-code-bracket')
                                                ->schema([
                                                    CustomMonacoEditor::make('head')
                                                        ->label('Head HTML')
                                                        ->language('html')
                                                        ->theme('blackboard')
                                                        ->fontSize('15px')
                                                        ->height('200px')
                                                        ->enablePreview(false)
                                                        ->columnSpanFull()
                                                        ->monacoId('email-template-head-editor')
                                                        ->live(onBlur: true)
                                                        ->afterStateUpdated(fn ($state, Set $set, Get $get) => static::updatePreviewSections($set, $get)),
                                                ])
                                                ->hidden(fn (Get $get) => (bool) $get('is_shared')),
                                            Forms\Components\Tabs\Tab::make('Content')
                                                ->icon('heroicon-o-code-bracket-square')
                                                ->schema([
                                                    CustomMonacoEditor::make('content')
                                                        ->label('Content HTML')
                                                        ->language('html')
                                                        ->theme('blackboard')
                                                        ->fontSize('15px')
                                                        ->height('340px')
                                                        ->enablePreview(false)
                                                        ->columnSpanFull()
                                                        ->monacoId('email-template-content-editor')
                                                        ->live(onBlur: true)
                                                        ->afterStateUpdated(fn ($state, Set $set, Get $get) => static::updatePreviewSections($set, $get)),
                                                ]),
                                            Forms\Components\Tabs\Tab::make('Foot')
                                                ->icon('heroicon-o-code-bracket')
                                                ->schema([
                                                    CustomMonacoEditor::make('foot')
                                                        ->label('Foot HTML')
                                                        ->language('html')
                                                        ->theme('blackboard')
                                                        ->fontSize('15px')
                                                        ->height('200px')
                                                        ->enablePreview(false)
                                                        ->columnSpanFull()
                                                        ->monacoId('email-template-foot-editor')
                                                        ->live(onBlur: true)
                                                        ->afterStateUpdated(fn ($state, Set $set, Get $get) => static::updatePreviewSections($set, $get)),
                                                ])
                                                ->hidden(fn (Get $get) => (bool) $get('is_shared')),
                                            Forms\Components\Tabs\Tab::make('Style')
                                                ->icon('heroicon-o-swatch')
                                                ->schema([
                                                    CustomMonacoEditor::make('style')
                                                        ->label('Style Block')
                                                        ->language('css')
                                                        ->theme('blackboard')
                                                        ->fontSize('15px')
                                                        ->height('220px')
                                                        ->enablePreview(false)
                                                        ->columnSpanFull()
                                                        ->monacoId('email-template-style-editor')
                                                        ->live(onBlur: true)
                                                        ->afterStateUpdated(fn ($state, Set $set, Get $get) => static::updatePreviewSections($set, $get)),
                                                ]),
                                            Forms\Components\Tabs\Tab::make('Script')
                                                ->icon('heroicon-o-command-line')
                                                ->schema([
                                                    CustomMonacoEditor::make('script')
                                                        ->label('Script Block')
                                                        ->language('javascript')
                                                        ->theme('blackboard')
                                                        ->fontSize('15px')
                                                        ->height('220px')
                                                        ->enablePreview(false)
                                                        ->columnSpanFull()
                                                        ->monacoId('email-template-script-editor')
                                                        ->live(onBlur: true)
                                                        ->afterStateUpdated(fn ($state, Set $set, Get $get) => static::updatePreviewSections($set, $get)),
                                                ]),
                                            Forms\Components\Tabs\Tab::make('metadata')
                                                ->label('Metadata')
                                                ->icon('heroicon-o-document-text')
                                                ->schema([
                                                    CustomMonacoEditor::make('metadata')
                                                        ->label('Template Variables (JSON)')
                                                        ->language('json')
                                                        ->theme('blackboard')
                                                        ->fontSize('15px')
                                                        ->height('220px')
                                                        ->enablePreview(false)
                                                        ->columnSpanFull()
                                                        ->monacoId('email-template-data-editor')
                                                        ->live(onBlur: true)
                                                        ->afterStateUpdated(fn ($state, Set $set, Get $get) => static::updatePreviewSections($set, $get)),
                                                ]),
                                        ])
                                            ->contained(false)
                                            ->columnSpanFull()
                                            ->persistTabInQueryString(),
                                ])
                                ->columnSpanFull(),
                        ])
                            ->columnSpan([
                                'default' => 12,
                                'xl' => 12,
                            ]),

                        
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable(),
                TextColumn::make('key')->searchable()->sortable(),
                TextColumn::make('name')->label('Title')->searchable()->sortable(),
                TextColumn::make('language_label')->label('Language'),
                TextColumn::make('created_at')->label('Created')->dateTime()->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(static::statusOptions()),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereRaw('LOWER(locale) = ?', ['en']);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmailTemplates::route('/'),
            'create' => Pages\CreateEmailTemplate::route('/create'),
            'edit' => Pages\EditEmailTemplate::route('/{record}/edit'),
        ];
    }

    private static function languageOptions(): array
    {
        $options = [];

        try {
            $apiService = resolve(TranslationApiService::class);
            $languagesResponse = $apiService->getLanguages(['limit' => 200]);
            $rawLanguages = [];

            if (isset($languagesResponse['data']) && is_array($languagesResponse['data'])) {
                $rawLanguages = $languagesResponse['data'];
            } elseif (is_array($languagesResponse) && !isset($languagesResponse['data'])) {
                $rawLanguages = $languagesResponse;
            }

            foreach ($rawLanguages as $language) {
                if (! isset($language['id'])) {
                    continue;
                }

                $flagCode = $language['flagCode'] ?? null;
                $nativeName = $language['languageFullNative'] ?? $language['languageFull'] ?? null;
                $iso = $language['languageISO2'] ?? $language['languageISO3'] ?? null;

                if ($nativeName === null) {
                    continue;
                }

                $safeName = htmlspecialchars((string) $nativeName, ENT_QUOTES, 'UTF-8');
                $isoMarkup = '';

                if ($iso !== null && trim((string) $iso) !== '') {
                    $safeIso = strtoupper(htmlspecialchars((string) $iso, ENT_QUOTES, 'UTF-8'));
                    $isoMarkup = sprintf(' <span class="language-iso">(%s)</span>', $safeIso);
                }

                $labelText = sprintf('<span class="language-name">%s%s</span>', $safeName, $isoMarkup);

                if ($flagCode) {
                    $safeFlag = strtolower(htmlspecialchars((string) $flagCode, ENT_QUOTES, 'UTF-8'));
                    $label = "<span class='flag-icon flag-icon-{$safeFlag} rounded me-2'></span>" . $labelText;
                } else {
                    $label = $labelText;
                }

                $options[(int) $language['id']] = $label;
            }
        } catch (\Throwable $exception) {
            report($exception);
        }

        return $options;
    }

    private static function defaultLanguageId(): ?int
    {
        try {
            $baseLanguage = Language::query()
                ->where('isBaseLanguage', true)
                ->first();

            if ($baseLanguage !== null) {
                return (int) $baseLanguage->id;
            }

            $english = Language::query()
                ->whereRaw('LOWER(languageISO2) = ?', ['en'])
                ->first();

            if ($english !== null) {
                return (int) $english->id;
            }

            $firstLanguageId = Language::query()
                ->orderBy('languageFull')
                ->value('id');

            return $firstLanguageId !== null ? (int) $firstLanguageId : null;
        } catch (\Throwable $exception) {
            report($exception);

            return null;
        }
    }

    private static function statusOptions(): array
    {
        return Collection::make(TemplateStatus::cases())
            ->mapWithKeys(static fn (TemplateStatus $status) => [$status->value => ucfirst(strtolower($status->value))])
            ->all();
    }

    private static function renderEmailSections(callable $get): array
    {
        $languageId = $get('language_id');
        $normalizedLanguageId = is_numeric($languageId) ? (int) $languageId : null;

        $data = [
            'key' => $get('key'),
            'language_id' => $normalizedLanguageId,
            'locale' => $get('locale'),
            'head' => $get('head'),
            'content' => $get('content'),
            'foot' => $get('foot'),
            'style' => $get('style'),
            'script' => $get('script'),
            'is_shared' => (bool) $get('is_shared'),
            'metadata' => $get('metadata'),
            'id_row' => $get('id_row'),
        ];

        $cacheKey = 'email_template_preview_'.md5(json_encode($data));

        $resolver = static function () use ($data) {
            $template = EmailTemplate::make($data);
            $variables = static::decodeMetadata($data['metadata'] ?? null);

            try {
                $sections = app(EmailTemplateRenderer::class)->render($template, $variables);

                if (! isset($sections['html'])) {
                    $sections['html'] = static::composeHtmlFromSections($sections, $data, $variables ?? []);
                }

                return $sections;
            } catch (Throwable $exception) {
                return [
                    'head' => $data['head'] ?? '',
                    'content' => $data['content'] ?? '',
                    'foot' => $data['foot'] ?? '',
                    'style' => $data['style'] ?? '',
                    'script' => $data['script'] ?? '',
                    'metadata' => $data['metadata'] ?? '',
                    'html' => static::composeHtmlFromSections($data, $data, $variables ?? []),
                    'error' => $exception->getMessage(),
                ];
            }
        };

        try {
            return Cache::remember($cacheKey, now()->addSeconds(2), $resolver);
        } catch (Throwable $exception) {
            report($exception);

            return $resolver();
        }
    }

    private static function composeHtmlFromSections(array $sections, array $context = [], array $variables = []): string
    {
        $locale = $context['locale'] ?? $sections['locale'] ?? null;
        $languageId = isset($context['language_id']) ? (int) $context['language_id'] : (isset($sections['language_id']) ? (int) $sections['language_id'] : null);

        [$lang, $direction] = static::determineLanguageAttributes($locale, $languageId, $variables);

        $langAttr = htmlspecialchars(str_replace('_', '-', $lang), ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
        $dirAttr = htmlspecialchars($direction, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');

        return sprintf(
            '<!DOCTYPE html><html lang="%s" dir="%s"><head><meta charset="utf-8"><title>Email Preview</title>%s<style>%s</style></head><body>%s%s<script>%s</script></body></html>',
            $langAttr,
            $dirAttr,
            $sections['head'] ?? '',
            $sections['style'] ?? '',
            $sections['content'] ?? '',
            $sections['foot'] ?? '',
            $sections['script'] ?? ''
        );
    }

    private static function determineLanguageAttributes(?string $locale, ?int $languageId, array $variables = []): array
    {
        $candidate = $variables['lang'] ?? null;

        if ($candidate === null || trim($candidate) === '') {
            $candidate = $locale;
        }

        $language = $languageId !== null
            ? Language::query()->firstWhere('id', $languageId)
            : null;

        return LocaleDirection::resolve($candidate, $language);
    }

    private static function updatePreviewSections(Set $set, Get $get): void
    {
        // Clear cache for this preview
        $languageId = $get('language_id');
        $normalizedLanguageId = is_numeric($languageId) ? (int) $languageId : null;

        $data = [
            'key' => $get('key'),
            'language_id' => $normalizedLanguageId,
            'locale' => $get('locale'),
            'head' => $get('head'),
            'content' => $get('content'),
            'foot' => $get('foot'),
            'style' => $get('style'),
            'script' => $get('script'),
            'is_shared' => (bool) $get('is_shared'),
            'metadata' => $get('metadata'),
            'id_row' => $get('id_row'),
        ];
        
        $cacheKey = 'email_template_preview_'.md5(json_encode($data));

        try {
            Cache::forget($cacheKey);
        } catch (Throwable $exception) {
            report($exception);
        }

        $set('preview_sections', static::renderEmailSections($get));
    }

    private static function handleLanguageSelection(Forms\Components\Select $component, mixed $state, Set $set, Get $get): void
    {
        if ($state === null) {
            return;
        }

        $livewire = $component->getLivewire();

        if (! $livewire instanceof Pages\EditEmailTemplate) {
            // Allow free selection during creation without data syncing
            return;
        }

        static::syncTemplateStateForLanguage((int) $state, $get, $set);
    }

    private static function syncTemplateStateForLanguage(int $languageId, Get $get, Set $set): void
    {
        $key = $get('key');

        if (! $key) {
            $set('language_id', $languageId);
            $set('id_row', null);

            return;
        }

        $template = EmailTemplate::findByKeyAndLanguage($key, $languageId);
        $baseTemplate = static::resolveBaseTemplate($key);
        $baseTemplateId = $baseTemplate?->id;

        if ($template === null) {
            $baseTemplate = static::resolveBaseTemplate($key);

            $set('editing_template_id', null);
            $set('language_id', $languageId);

            $motherId = null;
            if ($baseTemplate !== null && (int) $languageId !== (int) $baseTemplate->language_id) {
                $motherId = $baseTemplate->id;
            }
            $set('id_row', $motherId);

            $locale = static::resolveLocaleForLanguage($languageId, $get('locale'));
            if ($locale !== null) {
                $set('locale', strtolower($locale));
            }

            foreach (static::translatableFields() as $field) {
                $value = $baseTemplate?->{$field};

                if ($field === 'status') {
                    $value = $value instanceof TemplateStatus
                        ? $value->value
                        : ($value ?? TemplateStatus::DRAFT->value);
                }

                if ($field === 'is_shared') {
                    $value = (bool) ($value ?? false);
                }

                $set($field, $value);
            }
        } else {
            $set('editing_template_id', $template->id);
            $set('language_id', $template->language_id);

            $motherId = $template->id_row;
            if ($motherId === null && $baseTemplateId && $template->id !== $baseTemplateId) {
                $motherId = $baseTemplateId;
            }
            $set('id_row', $motherId);

            foreach (static::translatableFields() as $field) {
                $value = $template->{$field} ?? null;

                if ($baseTemplateId && $template->id !== $baseTemplateId && $baseTemplate !== null) {
                    if (in_array($field, ['style', 'script', 'metadata'], true)) {
                        $value = $baseTemplate->{$field};
                    }

                    if ($field === 'status') {
                        $value = $baseTemplate->status instanceof TemplateStatus
                            ? $baseTemplate->status->value
                            : $baseTemplate->status;
                    }
                }

                if ($field === 'status' && $value instanceof TemplateStatus) {
                    $value = $value->value;
                }

                if ($field === 'is_shared') {
                    $value = (bool) $value;
                }

                $set($field, $value);
            }

            if (! empty($template->locale)) {
                $set('locale', $template->locale);
            }
        }

        static::updatePreviewSections($set, $get);
    }

    private static function resolveBaseTemplate(?string $key): ?EmailTemplate
    {
        if ($key === null) {
            return null;
        }

        $baseLanguage = Language::query()
            ->where('isBaseLanguage', true)
            ->first();

        if ($baseLanguage !== null) {
            $template = EmailTemplate::findByKeyAndLanguage($key, (int) $baseLanguage['id']);

            if ($template !== null) {
                return $template;
            }
        }

        $englishTemplate = EmailTemplate::query()
            ->where('key', $key)
            ->where('locale', 'en')
            ->first();

        if ($englishTemplate !== null) {
            return $englishTemplate;
        }

        return EmailTemplate::query()
            ->where('key', $key)
            ->orderBy('id')
            ->first();
    }

    private static function resolveLocaleForLanguage(int $languageId, ?string $fallback = null): ?string
    {
        /** @var array|null $language */
        $language = Language::query()->firstWhere('id', $languageId)?->toArray();

        if ($language === null) {
            return $fallback;
        }

        $iso = $language['languageISO2'] ?? $fallback;

        return $iso !== null ? strtolower((string) $iso) : $fallback;
    }

    private static function translatableFields(): array
    {
        return [
            'name',
            'description',
            'status',
            'is_shared',
            'head',
            'content',
            'foot',
            'style',
            'script',
            'metadata',
        ];
    }

    private static function decodeMetadata(?string $json): array
    {
        if ($json === null || trim($json) === '') {
            return [];
        }

        try {
            $decoded = json_decode($json, true, 512, JSON_THROW_ON_ERROR);

            return is_array($decoded) ? $decoded : [];
        } catch (JsonException $exception) {
            Log::warning('Invalid email template preview metadata supplied', [
                'error' => $exception->getMessage(),
            ]);

            return [];
        }
    }
}
