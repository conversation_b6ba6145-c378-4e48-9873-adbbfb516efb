<?php

namespace App\Filament\Admin\Resources\OneTimeProductResource\Pages;

use App\Filament\Admin\Resources\OneTimeProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;

class ListOneTimeProducts extends ListRecords
{
    protected static string $resource = OneTimeProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.one_time_product.page_title'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'one_time_product',
                ])),
        ];
    }
}
