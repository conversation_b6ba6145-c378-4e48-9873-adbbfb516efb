<?php

namespace App\Filament\Admin\Resources;

use App\Constants\ConfigValueTypeEnum;
use App\Forms\Components\CustomMonacoEditor;
use App\Models\Config;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Filament\Admin\Resources\ConfigResource\Pages;

class ConfigResource extends Resource
{
    protected static ?string $model = Config::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Advanced Settings';

    protected static ?string $navigationGroup = 'Settings';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->label(__('Key'))
                            ->required()
                            ->unique(Config::class, 'key', ignoreRecord: true),

                        Forms\Components\Select::make('value_type')
                            ->label(__('Value Type'))
                            ->options(ConfigValueTypeEnum::toArray())
                            ->default(ConfigValueTypeEnum::STRING->value)
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                if ($state === ConfigValueTypeEnum::BOOLEAN->value) {
                                    $current = $get('value');
                                    if (is_string($current)) {
                                        $normalized = in_array(strtolower($current), ['1', 'true', 'on', 'yes'], true);
                                    } else {
                                        $normalized = (bool) $current;
                                    }
                                    $set('value', $normalized);
                                } else {
                                    $current = $get('value');
                                    if (is_bool($current)) {
                                        $set('value', $current ? '1' : '');
                                    }
                                }
                            })
                            ->extraAttributes([
                                'x-on:change' => "
                                    const isHtml = \$event.target.value === '" . ConfigValueTypeEnum::HTML->value . "';
                                    window.dispatchEvent(new CustomEvent('toggle-monaco-preview', {
                                        detail: {
                                            monacoId: 'config-value-editor',
                                            isDisplayed: isHtml
                                        }
                                    }));
                                "
                            ]),
                    ]),

                Forms\Components\Toggle::make('value')
                    ->label(__('Value'))
                    ->inline(false)
                    ->reactive()
                    ->afterStateHydrated(function (\Filament\Forms\Components\Toggle $component, $state) {
                        $component->state(in_array($state, ['1', 1, true, 'true'], true));
                    })
                    ->dehydrated(function (callable $get) {
                        return $get('value_type') === \App\Constants\ConfigValueTypeEnum::BOOLEAN->value;
                    })
                    ->visible(function (callable $get) {
                        $valueType = $get('value_type');
                        return $valueType === ConfigValueTypeEnum::BOOLEAN->value;
                    }),

                CustomMonacoEditor::make('value')
                    ->label(__('Value'))
                    ->reactive()
                    ->afterStateHydrated(function ($component, $state) {
                        if (!is_string($state)) {
                            $component->state((string) $state);
                        }
                    })
                    ->language(function (callable $get) {
                        $valueType = $get('value_type');
                        return $valueType === ConfigValueTypeEnum::JSON->value ? 'json' : 'html';
                    })
                    ->theme('blackboard')
                    ->fontSize('14px')
                    ->height('500px')
                    ->monacoId('config-value-editor')
                    ->dehydrated(function (callable $get) {
                        return $get('value_type') !== \App\Constants\ConfigValueTypeEnum::BOOLEAN->value;
                    })
                    ->hidden(function (callable $get) {
                        $valueType = $get('value_type');
                        return $valueType === ConfigValueTypeEnum::BOOLEAN->value;
                    })
                    ->enablePreview(function (callable $get) {
                        $valueType = $get('value_type');
                        return $valueType === ConfigValueTypeEnum::HTML->value;
                    })
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label(__('Key'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('value')
                    ->label(__('Value'))
                    ->formatStateUsing(function ($state, $record) {
                        if (($record->value_type ?? null) === ConfigValueTypeEnum::BOOLEAN->value) {
                            return in_array($state, ['1', 1, true, 'true'], true) ? 'true' : 'false';
                        }
                        return $state;
                    })
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->searchable(),

                Tables\Columns\TextColumn::make('value_type')
                    ->label(__('Value Type'))
                    ->sortable(),

                Tables\Columns\IconColumn::make('deletable')
                    ->label(__('Deletable'))
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn (Config $record): bool => $record->deletable)
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultPaginationPageOption('all')
            ->defaultSort('updated_at', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListConfigs::route('/'),
            'create' => Pages\CreateConfig::route('/create'),
            'edit' => Pages\EditConfig::route('/{record}/edit'),
        ];
    }

    public static function getBreadcrumb(): string
    {
        return 'Advanced Settings';
    }
}
