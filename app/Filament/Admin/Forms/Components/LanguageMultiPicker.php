<?php

namespace App\Filament\Admin\Forms\Components;

use Closure;
use Filament\Forms\Components\Concerns\HasOptions;
use Filament\Forms\Components\Concerns\HasPlaceholder;
use Filament\Forms\Components\Field;

class LanguageMultiPicker extends Field
{
    use HasOptions;
    use HasPlaceholder;

    protected string $view = 'filament.forms.components.language-multi-picker';

    protected string|Closure|null $searchPrompt = null;

    protected int|Closure $visibleLimit = 12;

    protected function setUp(): void
    {
        parent::setUp();

        $this->default([]);

        $this->afterStateHydrated(function (LanguageMultiPicker $component, $state): void {
            if (! is_array($state)) {
                $component->state([]);

                return;
            }

            $component->state(array_values($state));
        });

        $this->dehydrateStateUsing(static function (LanguageMultiPicker $component, $state): array {
            if (! is_array($state)) {
                return [];
            }

            return array_values(array_filter(
                $state,
                static fn ($value) => $value !== null && $value !== ''
            ));
        });
    }

    public function searchPrompt(string|Closure|null $searchPrompt): static
    {
        $this->searchPrompt = $searchPrompt;

        return $this;
    }

    public function getSearchPrompt(): string
    {
        $prompt = $this->evaluate($this->searchPrompt);

        if (is_string($prompt) && $prompt !== '') {
            return $prompt;
        }

        return __('Search languages...');
    }

    public function visibleLimit(int|Closure $limit): static
    {
        $this->visibleLimit = $limit;

        return $this;
    }

    public function getVisibleLimit(): int
    {
        return (int) $this->evaluate($this->visibleLimit);
    }
}
