<?php

namespace App\Filament\Admin\Pages;

use App\Services\ConfigService;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;

class OpenGraphImageSettings extends Page
{
    protected static string $view = 'filament.admin.pages.open-graph-image-settings';

    protected static ?string $navigationGroup = 'Settings';

    public static function canAccess(): bool
    {
        $configService = app()->make(ConfigService::class);

        return $configService->isAdminSettingsEnabled()
            && Auth::user()
            && Auth::user()->hasPermissionTo('update-settings');
    }

    public static function getNavigationLabel(): string
    {
        return __('Open Graph Images');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('featureDescriptionDrawer')
                ->label('?')
                ->tooltip(t('core.common.about_this_page'))
                ->color('gray')
                ->modalHeading(t('core.settings.open_graph_images'))
                ->modalDescription('')
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->slideOver()
                ->modalWidth(MaxWidth::SevenExtraLarge)
                ->modalContent(view('filament.admin.partials.feature-description-drawer', [
                    'featureKey' => 'open_graph_images',
                ])),
        ];
    }
}
