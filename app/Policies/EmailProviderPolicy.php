<?php

namespace App\Policies;

use App\Models\EmailProvider;
use App\Models\User;

class EmailProviderPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('update-settings');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, EmailProvider $emailProvider): bool
    {
        return $user->hasPermissionTo('update-settings');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('update-settings');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, EmailProvider $emailProvider): bool
    {
        return $user->hasPermissionTo('update-settings');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, EmailProvider $emailProvider): bool
    {
        return $user->hasPermissionTo('update-settings');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, EmailProvider $emailProvider): bool
    {
        return $user->hasPermissionTo('update-settings');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, EmailProvider $emailProvider): bool
    {
        return $user->hasPermissionTo('update-settings');
    }
}
