<?php

namespace App\Http\Controllers\Api;

use App\Constants\TemplateStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEmailTemplateRequest;
use App\Http\Requests\UpdateEmailTemplateRequest;
use App\Http\Resources\EmailTemplateResource;
use App\Models\EmailTemplate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Services\EmailTemplateRenderer;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Throwable;

class EmailTemplateController extends Controller
{
    public function __construct(private readonly EmailTemplateRenderer $renderer)
    {
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = (int) $request->input('per_page', 15);
            $orderBy = $request->input('order_by', 'updated_at');
            $orderDirection = $request->input('order', 'desc');

            $allowedOrderBy = ['id', 'key', 'name', 'locale', 'language_id', 'status', 'created_at', 'updated_at'];
            if (!in_array($orderBy, $allowedOrderBy, true)) {
                $orderBy = 'updated_at';
            }

            $allowedDirections = ['asc', 'desc'];
            if (!in_array(strtolower($orderDirection), $allowedDirections, true)) {
                $orderDirection = 'desc';
            }

            $query = EmailTemplate::query();

            if ($request->filled('search')) {
                $query->where(function ($builder) use ($request) {
                    $search = '%' . $request->input('search') . '%';
                    $builder->where('key', 'like', $search)
                        ->orWhere('name', 'like', $search)
                        ->orWhere('description', 'like', $search);
                });
            }

            if ($request->filled('status') && TemplateStatus::tryFrom($request->input('status'))) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('language_id')) {
                $query->where('language_id', (int) $request->input('language_id'));
            } elseif ($request->filled('locale')) {
                $query->where('locale', $request->input('locale'));
            }

            $templates = $query
                ->orderBy($orderBy, $orderDirection)
                ->paginate($perPage)
                ->appends($request->query());

            return EmailTemplateResource::collection($templates)
                ->additional([
                    'success' => true,
                    'message' => 'Email templates retrieved successfully.',
                ])
                ->response();
        } catch (Throwable $exception) {
            Log::error('Failed to list email templates.', [
                'exception' => $exception,
                'query' => $request->query(),
            ]);

            return $this->errorResponse(
                'An unexpected error occurred while retrieving email templates.',
                HttpResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function store(StoreEmailTemplateRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            if (($data['status'] ?? null) === null) {
                $data['status'] = TemplateStatus::DRAFT->value;
            }
            $data['created_by'] = $request->user()?->id;

            $template = EmailTemplate::create($data);

            return (new EmailTemplateResource($template))
                ->additional([
                    'success' => true,
                    'message' => 'Email template created successfully.',
                ])
                ->response()
                ->setStatusCode(HttpResponse::HTTP_CREATED);
        } catch (Throwable $exception) {
            Log::error('Failed to create email template.', [
                'exception' => $exception,
                'payload' => $request->all(),
                'user_id' => $request->user()?->id,
            ]);

            return $this->errorResponse(
                'An unexpected error occurred while creating the email template.',
                HttpResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function show(EmailTemplate $emailTemplate): JsonResponse
    {
        try {
            return (new EmailTemplateResource($emailTemplate))
                ->additional([
                    'success' => true,
                    'message' => 'Email template retrieved successfully.',
                ])
                ->response();
        } catch (Throwable $exception) {
            Log::error('Failed to show email template.', [
                'exception' => $exception,
                'template_id' => $emailTemplate->id,
            ]);

            return $this->errorResponse(
                'An unexpected error occurred while retrieving the email template.',
                HttpResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function update(UpdateEmailTemplateRequest $request, EmailTemplate $emailTemplate): JsonResponse
    {
        try {
            $data = $request->validated();
            if (array_key_exists('status', $data) && $data['status'] === null) {
                $data['status'] = TemplateStatus::DRAFT->value;
            }

            $emailTemplate->update($data);

            return (new EmailTemplateResource($emailTemplate))
                ->additional([
                    'success' => true,
                    'message' => 'Email template updated successfully.',
                ])
                ->response();
        } catch (Throwable $exception) {
            Log::error('Failed to update email template.', [
                'exception' => $exception,
                'template_id' => $emailTemplate->id,
                'payload' => $request->all(),
                'user_id' => $request->user()?->id,
            ]);

            return $this->errorResponse(
                'An unexpected error occurred while updating the email template.',
                HttpResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function destroy(EmailTemplate $emailTemplate): JsonResponse
    {
        try {
            $emailTemplate->delete();

            return response()->json([
                'success' => true,
                'message' => 'Email template deleted successfully.',
            ]);
        } catch (Throwable $exception) {
            Log::error('Failed to delete email template.', [
                'exception' => $exception,
                'template_id' => $emailTemplate->id,
            ]);

            return $this->errorResponse(
                'An unexpected error occurred while deleting the email template.',
                HttpResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function render(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'key' => ['required', 'string'],
                'payload' => ['nullable', 'array'],
                'language_id' => ['nullable', 'integer'],
                'locale' => ['nullable', 'string', 'max:10'],
            ]);

            $templateQuery = EmailTemplate::query()
                ->where('key', $data['key']);

            if (!empty($data['language_id'])) {
                $templateQuery->where('language_id', (int) $data['language_id']);
            } elseif (!empty($data['locale'])) {
                $templateQuery->where('locale', strtolower((string) $data['locale']));
            }

            $template = $templateQuery->first();

            if ($template === null) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email template not found.',
                ], HttpResponse::HTTP_NOT_FOUND);
            }

            $rendered = $this->renderer->render($template, $data['payload'] ?? []);

            return response()->json([
                'success' => true,
                'message' => 'Email template rendered successfully.',
                'data' => $rendered,
            ]);
        } catch (Throwable $exception) {
            Log::error('Failed to render email template.', [
                'exception' => $exception,
                'key' => $request->input('key'),
                'payload' => $request->input('payload'),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred while rendering the email template.',
            ], HttpResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function errorResponse(string $message, int $status, array $data = []): JsonResponse
    {
        return EmailTemplateResource::collection(collect())
            ->additional(array_merge([
                'success' => false,
                'message' => $message,
            ], $data))
            ->response()
            ->setStatusCode($status);
    }
}
