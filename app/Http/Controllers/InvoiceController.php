<?php

namespace App\Http\Controllers;

use App\Constants\PaymentProviderConstants;
use App\Models\Receipt;
use App\Models\Transaction;
use App\Models\User;
use App\Services\CalculationService;
use App\Services\InvoiceService;
use App\Services\PaymentProviders\PaymentService;
use App\Models\Cart;
use App\Models\Currency;
use Illuminate\Support\Str;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    public function __construct(
        private InvoiceService $invoiceService,
        private CalculationService $calculationService,
        private PaymentService $paymentService
    ) {}

    public function generateCustomerInfoPdfVars($customerJson, array $customData){
        $addressLines = [
            ($customData['address_line_1'] ?? '') ?: $customerJson['address']['address_line_1'] ?? '',
            ($customData['address_line_2'] ?? '') ?: $customerJson['address']['address_line_2'] ?? '',
        ];
        $addressLines = array_filter($addressLines, function($line) {
            return strlen($line) > 0;
        });
        $addressStr = implode(', ', $addressLines);
        $customerInfo =  [
            "customerName" => ($customData['displayed_name'] ?? '') ?: $this->invoiceService->getInvoiceCustomerName($customerJson),
            "postalCode" => $customerJson['address']['zip'] ?? '',
            "city" =>   ($customData['city'] ?? '') ?: $customerJson['address']['city'] ?? '',
            "country" =>  ($customData['country'] ?? '') ?:  (isset($customerJson['address']['country']) ?   $customerJson['address']['country']['name'] : ''),
            "streetAddress" => $addressStr ?? '',
            "mobileNumber" => ($customData['phone'] ?? '') ?: $customerJson['address']['phone'] ?? '',
            "email" => ($customData['email'] ?? '') ?: $customerJson['email'] ?? '',
        ];
        return $customerInfo;
    }
    public function fetchStripeObject($stripeSubscriptionId) {
        /** @var StripeProvider $paymentProviderStrategy */
        $paymentProviderStrategy = $this->paymentService->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );
        return $paymentProviderStrategy->fetchStripeObject($stripeSubscriptionId);
    }
    public function getStripeMetadata($stripeObject){
        if($stripeObject === null) {
            return [];
        }
        if($stripeObject->object === 'invoice'){
            return $stripeObject->lines->data[0]->metadata ?? [];
        }
        if($stripeObject->object === 'checkout.session'){
            return $stripeObject->metadata ?? [];
        }
        return $stripeObject->metadata ?? [];
    }
    public function getStripeReference($docType, $transaction, $receipt){
        $stripeReference = null;
        if($docType === 'receipt') {
            $stripeReference = $receipt->payment_provider_object_id;
        }else {
            $stripeReference = $transaction->payment_provider_reference ?? $transaction->payment_provider_transaction_id;
        }

        return  $stripeReference;

    }
    public function generatePdfVars(
        User $user,
        string $docType,
        Transaction $transaction,
        ?Receipt $receipt,
        array $customData
    ){



        $cdnUrl = config('services.cdn.url');
        $translationUrl = config('services.translation_api.url');

        // Ensure sane defaults for printable settings
        $colorThemeConfig = config('invoices_receipts.color_theme');
        $colorTheme = json_decode($colorThemeConfig, true);
        if (! is_array($colorTheme)) {
            $colorTheme = [
                'primary' => '#4f46e5', // indigo-600 as a safe default
                'accent' => '#ffffff',  // white text on primary backgrounds
                'docTypeColor' => '#111827', // gray-900 for headings
            ];
        } else {
            $colorTheme = array_merge([
                'primary' => '#4f46e5',
                'accent' => '#ffffff',
                'docTypeColor' => '#111827',
            ], $colorTheme);
        }

        $companyDataConfig = config('invoices_receipts.company_data');
        $companyData = json_decode($companyDataConfig, true);
        if (! is_array($companyData)) {
            $companyData = [
                'companyName' => config('app.name'),
                'street' => '',
                'suite' => '',
                'city' => '',
                'country' => '',
                'email' => config('mail.from.address') ?? '',
                'code' => '',
                'vat' => '',
                'phone' => '',
            ];
        } else {
            $companyData = array_merge([
                'companyName' => config('app.name'),
                'street' => '',
                'suite' => '',
                'city' => '',
                'country' => '',
                'email' => config('mail.from.address') ?? '',
                'code' => '',
                'vat' => '',
                'phone' => '',
            ], $companyData);
        }


        $language = $user->settings["base_language"] ?? 'en';
        $customerJson = $transaction->customer_json;
        $planJson = $transaction->cart->plan_json;
        $subscriptionJson = $transaction->subscription_json;
        $customerInfo = $this->generateCustomerInfoPdfVars($customerJson, $customData);
        $stripeObject = $this->fetchStripeObject($transaction->payment_provider_transaction_id);
        $stripeMetadata = $this->getStripeMetadata($stripeObject);
        $planPriceAmount = $this->calculationService->getPlanPriceFromArray($transaction->cart->plan_json)['price'];


        $accounting = json_decode($stripeMetadata['accountingJsonStr'] ?? 'null', true) ?? [
                        'amountToPay' => $transaction->amount,
                        'amountToRefund' => 0,
                        'total' => $transaction->amount,
        ];
        if($receipt){
            $accounting = array_merge($accounting, [
                "amountAlreadyPaid" => $receipt->amount_already_paid ?? 0,
                "remainingToPay" => $receipt->remaining_to_pay ?? 0,
                "amountPaid" =>  $receipt->amount_paid ?? 0,
            ]);
        }
        $templateVars =  [
                "currencyCode" => $transaction->currency->code,
                "docType" => $docType,
                "language" => $language,
                "customerInfo" => $customerInfo,
                "payment" => [
                    "accounting" => $accounting,
                    "expectedPaymentDate" => $transaction->created_at->format('Y-m-d'),
                    "effectivePaymentDate" => $receipt ? $receipt->created_at->format('Y-m-d'): null,
                    "paymentMethod" => "Stripe",
                    "stripeReference" => $this->getStripeReference($docType, $transaction, $receipt),
                    "invoiceNumber" =>  $transaction->invoice_number,
                    "receiptNumber" => $receipt ? $receipt->receipt_number : null,
                    "subtotal" => $planPriceAmount,
                    "total" => $transaction->amount,
                    "amountPaid" => $transaction->amount,
                ],
                "product" => [
                    [
                        "name" => $planJson['product']['name'],
                        "quantity" => 1,
                        "billingPeriodIntervalAdv" => $planJson['interval']['adverb'],
                        "unitPrice" => $planPriceAmount,
                        "price" => $planPriceAmount,

                        "startedDate" => $subscriptionJson ? $subscriptionJson['ends_at']: null,
                        "expiredDate" =>$subscriptionJson ? $subscriptionJson['starts_at']: null,
                        "userCount" => $planJson['product']['user_count'] ?? 1,
                        "creditCount" => $planJson['product']['credit_count'],
                        "productFeatures" => $planJson['product']['features'],

                    ],
                ],
            ];
        $data = [
            "templateVars" => $templateVars,
            "env" => [
                "TRANSLATION_API_URL" => $translationUrl,
                "CDN_URL" =>  $cdnUrl,
            ],
            "settings" => [
                "backgroundImageUrl" => str_replace('{{CDN_URL}}',  $cdnUrl ?? '', config('invoices_receipts.background_image_url')),
                "colorTheme" => $colorTheme,
                "companyData" => $companyData,
                "logoUrl" => str_replace('{{CDN_URL}}', $cdnUrl ?? '', config('invoices_receipts.logo_url')),
                "appName" => config('app.name')
            ]
        ];
        return $data;
    }
    public function generatePdf(string $docType, string $uuid){
        $transaction = null;
        $receipt = null;



        if($docType == "receipt") {
            $receipt = Receipt::where('uuid', $uuid)->firstOrFail();
            $transaction = Transaction::with(['cart'])->where('id', $receipt->transaction_id)->firstOrFail();
        }else {
            $transaction = Transaction::with(['cart'])->where('uuid', $uuid)->firstOrFail();
        }


        $user = auth()->user();
        if(!$user || ($user->id !== $transaction->user_id && !$user->isAdmin())){
            abort(403);
        }
        // Retrieve custom data from query parameters
        $customDataStr = request()->query('customData');
        $customData = ($customDataStr && $user->isAdmin()) ? json_decode(request()->query('customData'), true) : [];

        $pdfVars = $this->generatePdfVars($user, $docType, $transaction, $receipt, $customData);

        // Create Gotenberg client
        $gotenbergUrl = config('services.gotenberg.url');
        if (empty($gotenbergUrl) || ! is_string($gotenbergUrl)) {
            return response()->json([
                'error' => 'Gotenberg URL not configured. Set GOTENBERG_URL in your .env (e.g., http://localhost:3000).'], 500);
        }
        $client = Gotenberg::chromium($gotenbergUrl);




        // Build HTMLRequest
        $outputFilename =  ($docType == "receipt" ? 'receipt_' : 'invoice_') . $transaction->uuid;
        $request = $client->pdf()->outputFilename($outputFilename)->margins('10mm', '10mm', '10mm', '10mm');
        $pages = [

                'pdf.accounting.partials.fonts' => $pdfVars,
                'pdf.accounting.partials.style' => $pdfVars,
                'pdf.accounting.'.$docType => $pdfVars,

        ];

        $request->skipNetworkIdleEvent(false);
        // CONSTRUCT HTML
        $html = '';
        foreach ($pages as $blade => $pageData) {
            $html .= view($blade, $pageData)->render();
        }

        // Save the html
        // file_put_contents(storage_path('tmp/indexx.html'), $html);

        $request = $request->html(Stream::string('index.html', $html));
        $response = Gotenberg::send($request);
        if ($response->getStatusCode() === 200) {
            $fileContent = $response->getBody()->getContents();
            return response($fileContent)
                ->header('Content-Type', 'application/pdf')  // Set the MIME type for PDF
                ->header('Content-Disposition', 'attachment; filename="' . $outputFilename . '.pdf"') ;
        }
        return response()->json(['error' => 'Failed to generate PDF'], 500);
    }

    public function generate(string $transactionUuid)
    {
        $transaction = Transaction::where('uuid', $transactionUuid)->firstOrFail();

        $forceRegenerate = request()->boolean('regenerate', false) && auth()->user()->isAdmin();


        $result = $this->invoiceService->generate($transaction, $forceRegenerate);

        if ($result === null) {
            abort(404);
        }

        return $result;
    }

    /**
     * Preview invoice (used to generate PDF and show it from admin panel)
     */
    public function preview(Request $request)
    {
        if (! auth()->user()->isAdmin()) {
            abort(403);
        }

        // Build a lightweight in-memory Transaction so we can reuse generatePdfVars
        $user = auth()->user();

        // Resolve default currency for price mapping
        $defaultCurrencyCode = config('app.default_currency', 'USD');
        $currency = Currency::where('code', $defaultCurrencyCode)->first() ?? Currency::first();

        $planPrice = 100; // sample price
        $planJson = [
            'product' => [
                'name' => 'Sample Plan',
                'user_count' => 1,
                'credit_count' => 100,
                'features' => [
                    ['key' => 'ai_projects'],
                    ['key' => 'priority_support'],
                ],
            ],
            'interval' => [
                'adverb' => 'monthly',
            ],
            'prices' => [
                [
                    'currency_id' => $currency?->id ?? 1,
                    'price' => $planPrice,
                    'price_per_unit' => $planPrice,
                ],
            ],
        ];

        $cart = new Cart([
            'plan_json' => $planJson,
        ]);

        $transaction = new Transaction([
            'uuid' => (string) Str::uuid(),
            'user_id' => $user->id,
            'amount' => $planPrice,
            'total_tax' => 0,
            'total_discount' => 0,
            'total_fees' => 0,
            'subscription_json' => null,
            'customer_json' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'address' => [
                    'address_line_1' => '123 Main St',
                    'address_line_2' => '',
                    'zip' => '12345',
                    'city' => 'Metropolis',
                    'country' => ['name' => 'United States'],
                    'phone' => '******-123-4567',
                ],
            ],
            'invoice_number' => ($request->get('serial_number_series') ?? 'INV') . '-PREVIEW',
        ]);
        $transaction->created_at = now();
        $transaction->setRelation('cart', $cart);
        if ($currency) {
            $transaction->setRelation('currency', $currency);
        }

        $pdfVars = $this->generatePdfVars($user, 'invoice', $transaction, null);

        // Optionally override company data with request (keeps config as fallback)
        $companyData = $pdfVars['settings']['companyData'] ?? [];
        $pdfVars['settings']['companyData'] = array_merge($companyData, array_filter([
            'companyName' => $request->get('seller_name'),
            // Map address to a single line as 'street' if provided
            'street' => $request->get('seller_address'),
            // Additional optional fields from settings form
            'code' => $request->get('seller_code'),
            'vat' => $request->get('seller_tax_number'),
            'phone' => $request->get('seller_phone'),
            'email' => $request->get('seller_email'),
        ], fn ($v) => filled($v)));

        // Create Gotenberg client and render the same templates as generatePdf()
        $gotenbergUrl = config('services.gotenberg.url');
        if (empty($gotenbergUrl) || ! is_string($gotenbergUrl)) {
            return response(
                '<div style="padding:16px;font-family:system-ui,Segoe UI,Roboto,Helvetica,Arial,sans-serif;line-height:1.5">'
                . '<h2 style="margin:0 0 8px">PDF preview unavailable</h2>'
                . '<p style="margin:0 0 4px">GOTENBERG_URL is not configured in your .env.</p>'
                . '<p style="margin:0 0 12px">Add for example: <code>GOTENBERG_URL=http://localhost:3000</code></p>'
                . '<small>Set the URL to your running Gotenberg service, then reload.</small>'
                . '</div>',
                500,
                ['Content-Type' => 'text/html']
            );
        }
        $client = Gotenberg::chromium($gotenbergUrl);

        $outputFilename = 'invoice_preview_' . $transaction->uuid;
        $requestPdf = $client->pdf()->outputFilename($outputFilename)->margins('10mm', '10mm', '10mm', '10mm');
        $pages = [
            'pdf.accounting.partials.fonts' => $pdfVars,
            'pdf.accounting.partials.style' => $pdfVars,
            'pdf.accounting.invoice' => $pdfVars,
        ];

        $requestPdf->skipNetworkIdleEvent(false);

        $html = '';
        foreach ($pages as $blade => $pageData) {
            $html .= view($blade, $pageData)->render();
        }

        $requestPdf = $requestPdf->html(Stream::string('index.html', $html));
        $response = Gotenberg::send($requestPdf);
        if ($response->getStatusCode() === 200) {
            $fileContent = $response->getBody()->getContents();
            return response($fileContent)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'inline; filename="' . $outputFilename . '.pdf"');
        }

        return response()->json(['error' => 'Failed to generate PDF preview'], 500);
    }
}
