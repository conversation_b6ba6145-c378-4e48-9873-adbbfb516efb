<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmailTemplateResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'key' => $this->key,
            'name' => $this->name,
            'description' => $this->description,
            'language_id' => $this->language_id,
            'locale' => $this->locale,
            'language' => $this->language_details,
            'language_label' => $this->language_label,
            'status' => $this->status,
            'is_shared' => (bool) $this->is_shared,
            'head' => $this->head,
            'content' => $this->content,
            'foot' => $this->foot,
            'style' => $this->style,
            'script' => $this->script,
            'created_by' => $this->created_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
        ];
    }
}
