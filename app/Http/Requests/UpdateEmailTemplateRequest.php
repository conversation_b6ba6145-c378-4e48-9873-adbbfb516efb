<?php

namespace App\Http\Requests;

use App\Constants\TemplateStatus;
use App\Models\EmailTemplate;
use App\Models\Language;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Illuminate\Validation\Rule;

class UpdateEmailTemplateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $emailTemplate = $this->route('email_template');
        $emailTemplateId = is_object($emailTemplate) ? $emailTemplate->id : $emailTemplate;
        $currentLanguageId = is_object($emailTemplate) ? $emailTemplate->language_id : null;
        $isTranslation = $emailTemplate instanceof EmailTemplate && $emailTemplate->id_row !== null;

        return [
            'key' => [
                'sometimes',
                $isTranslation ? 'nullable' : 'required',
                'string',
                'max:255',
                Rule::unique('email_templates', 'key')
                    ->ignore($emailTemplateId)
                    ->where(function ($query) use ($currentLanguageId) {
                        $languageId = $this->input('language_id', $currentLanguageId);

                        return $languageId !== null
                            ? $query->where('language_id', (int) $languageId)
                            : $query;
                    }),
            ],
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'language_id' => array_merge(
                ['sometimes', 'required', 'integer'],
                $this->languageValidationRule()
            ),
            'locale' => ['sometimes', 'nullable', 'string', 'max:255'],
            'status' => ['sometimes', 'nullable', Rule::enum(TemplateStatus::class)],
            'is_shared' => ['sometimes', 'boolean'],
            'head' => ['nullable', 'string'],
            'content' => ['nullable', 'string'],
            'foot' => ['nullable', 'string'],
            'style' => ['nullable', 'string'],
            'script' => ['nullable', 'string'],
            'id_row' => ['sometimes', 'nullable', 'integer', 'exists:email_templates,id'],
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($this->filled('language_id')) {
            $language = $this->resolveLanguage((int) $this->input('language_id'));

            if ($language !== null) {
                $this->merge([
                    'language_id' => (int) $language['id'],
                    'locale' => strtolower((string) ($language['languageISO2'] ?? $this->input('locale'))),
                ]);
            }
        }
    }

    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'message' => 'Validation failed.',
            'errors' => $validator->errors(),
        ], HttpResponse::HTTP_UNPROCESSABLE_ENTITY));
    }

    private function languageValidationRule(): array
    {
        $ids = $this->availableLanguageIds();

        if (empty($ids)) {
            return [];
        }

        return [
            Rule::in($ids),
        ];
    }

    private function availableLanguageIds(): array
    {
        return Language::query()->pluck('id')->map(fn ($id) => (int) $id)->all();
    }

    private function resolveLanguage(int $languageId): ?array
    {
        return Language::query()->firstWhere('id', $languageId)?->toArray();
    }
}
