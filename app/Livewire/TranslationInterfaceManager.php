<?php
namespace App\Livewire;

use App\Filament\Admin\Resources\TranslationInterfaceResource;
use App\Services\AppService;
use App\Services\TranslationApiService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithPagination;
use RecursiveCallbackFilterIterator;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;

class TranslationInterfaceManager extends Component
{
    use WithPagination;

    public array $languageOptions = [];
    public $selectedLanguage;
    public $selectedComparisonLanguage;
    public $interfaceKeysProperty;
    public $languageStats;
    public $selectedLanguageStats;
    public $translatedFilter = 'all';
    public $selectedApp;
    public $apps = [];

    public string $searchTerm = '';

    public int $perPage = 100;

    // --- Edit Modal Properties (as before) ---
    public bool $showEditModal = false;
    public ?array $editingItem = null;
    public ?int $editingItemId = null;
    public string $editFormKey = '';
    public string $editFormBaseValue = '';
    public string $editFormComparisonValue = '';

    // --- Create Modal Properties ---
    public string $createFormKey = '';
    public string $createFormBaseValue = '';
    public string $createFormComparisonValue = '';
    public $createFormAppId = 0;
    public $languageId = 1;

    public string $searchBy = 'key';

    protected TranslationApiService $apiService;
    protected AppService $appService;

    public function boot(TranslationApiService $apiService, AppService $appService)
    {
        $this->apiService = $apiService;
        $this->appService = $appService;
    }

    public function mount()
    {
        $this->loadApps();
        $this->loadLanguageStats();
        $this->loadLanguageOptions();

        if (!empty($this->languageOptions)) {
            $languageCollection = collect($this->languageOptions);

            $englishLanguage = $languageCollection->firstWhere('languageFull', 'English');

            if ($englishLanguage) {
                $this->selectedComparisonLanguage = $englishLanguage;
            } else {
                if (isset($this->languageOptions[0])) {
                    $this->selectedComparisonLanguage = $this->languageOptions[0];
                } else {
                    $this->selectedComparisonLanguage = null;
                }
            }
        } else {
            $this->selectedComparisonLanguage = null;
        }

        // $this->selectedComparisonLanguage = null;
    }

    public function loadLanguageOptions()
    {
        $languagesApiResponse = $this->apiService->getLanguages(['limit' => 1000]);
        $rawLanguageItems = [];

        if (isset($languagesApiResponse['data']) && is_array($languagesApiResponse['data'])) {
            $rawLanguageItems = $languagesApiResponse['data'];
        } elseif (is_array($languagesApiResponse) && !isset($languagesApiResponse['data'])) {
            $rawLanguageItems = $languagesApiResponse;
        }

        $optionsWithFullData = [];
        if (!empty($rawLanguageItems)) {
            foreach ($rawLanguageItems as $language) {
                if (isset($language['id'])) {
                    $optionsWithFullData[$language['id']] = $language;
                }
            }
        }

        $options = [];
        if (!empty($rawLanguageItems)) {
            foreach ($rawLanguageItems as $language) {
                if (isset($language['id']) && isset($language['languageFull'])) {
                    $options[] = [
                        'id' => $language['id'],
                        'languageFull' => $language['languageFull'],
                        'flagCode' => $language['flagCode'] ?? null,
                    ];
                }
            }
        }

        // dd($optionsWithFullData);

        $this->languageOptions = $options;

        // if (!empty($rawLanguageItems)) {
        //     $this->simpleLanguageSelectOptions = Arr::pluck($rawLanguageItems, 'languageFull', 'id');
        // } else {
        //     $this->simpleLanguageSelectOptions = [];
        // }
    }

    public function loadLanguageStats()
    {
        $this->languageStats = $this->apiService->getInterfaceTranslationKeyStat();

        $this->selectedLanguageStats = $this->apiService->getLanguageStat((int) $this->selectedLanguage);
    }

    public function loadApps()
    {
        $this->apps = $this->appService->getApps();
    }

    public function updatedSelectedLanguage($value)
    {
        // Log::info('Hook: updatedSelectedLanguage called.', ['new_value' => $value]);
        $this->resetPage();
    }

    public function updatedSelectedComparisonLanguage($value)
    {
        // Log::info('Hook: updatedSelectedComparisonLanguage called.', ['new_value' => $value]);
        $this->resetPage();
    }

    public function updatedSearchTerm($value)
    {
        // Log::info('Hook: updatedSearchTerm called.', ['new_value' => $value]);
        $this->resetPage();
    }

    public function updatedTranslatedFilter($value)
    {
        // Log::info('Hook: updatedTranslatedFilter called.', ['new_value' => $value]);
        $this->resetPage();
    }

    public function updatedSelectedApp($value)
    {
        // Log::info('Hook: updatedSelectedApp called.', ['new_value' => $value]);
        $this->resetPage();
    }

    public function getInterfaceKeysProperty()
    {
        if (!$this->selectedLanguage || !$this->selectedComparisonLanguage) {
            return ['data' => [], 'total' => 0];
        }

        $this->selectedLanguageStats = $this->apiService->getLanguageStat((int) $this->selectedLanguage['id']);

        $queryParams = [
            'limit' => $this->perPage,
            'offset' => ($this->paginators['page'] - 1) * $this->perPage,
            'translatedFilter' => $this->translatedFilter,
        ];

        if ($this->searchTerm) {
            if ($this->searchBy === 'value') {
                $searchQuery = [
                    'valueSearch' => $this->searchTerm,
                    'searchOption' => 'LIKE',
                ];
            } else {
                $searchQuery = [
                    'searchOption' => 'LIKE',
                    'where' => json_encode(['key' => $this->searchTerm]),
                ];
            }

            $queryParams = array_merge($queryParams, $searchQuery);
        }

        if ($this->selectedApp >= 0) {
            $searchQuery = [
                'appId' => $this->selectedApp,
            ];

            $queryParams = array_merge($queryParams, $searchQuery);
        }

        $response = $this->apiService->getInterfaceTranslationKey(
            $this->selectedLanguage['id'],
            $this->selectedComparisonLanguage['id'],
            $queryParams
        );

        $data = $response['data'] ?? ($response ?? []);
        $total = $response['meta']['itemCount'] ?? ($response['total'] ?? count($data));

        return ["data" => $data, "total" => $total];
    }

    public function updatedSearchBy()
    {
        $this->resetPage();
    }

    public function closeEditModal()
    {
        $this->showEditModal = false;
        $this->reset(['editingItem', 'editingItemId', 'editFormKey', 'editFormBaseValue', 'editFormComparisonValue']);
    }

    public function saveEditedItem()
    {
        if (!$this->editingItemId || !$this->editingItem) {
            $this->dispatch('notify', message: 'Error: No item selected for saving.', type: 'error');
            return;
        }

        // Basic validation (use Laravel validation rules for more complex scenarios)
        if (empty($this->editFormKey) || empty($this->editFormBaseValue)) {
            $this->dispatch('notify', message: 'Key and Base Value cannot be empty.', type: 'warning');
            // You can also use Livewire's built-in validation:
            $this->validate([
                // 'editFormKey' => 'required|string',
                'editFormBaseValue' => 'required|string',
            ]);
            return;
        }

        // Prepare data for the API update
        // The structure depends on what your API update endpoint expects
        // This is a placeholder structure.
        $updateData = [
            'id' => $this->editingItemId, // Usually the ID is in the URL for PATCH/PUT
            'key' => $this->editFormKey,
            // Assuming your API expects values for specific languages
            // You'll need to know the language IDs for base and comparison
            'translations' => [
                // Example: if selectedLanguage holds the full object
                ['languageId' => $this->selectedLanguage['id'], 'value' => $this->editFormBaseValue],
            ]
        ];

        // If there's a comparison value and it's different, include it
        if ($this->selectedComparisonLanguage && $this->selectedComparisonLanguage['id'] !== $this->selectedLanguage['id']) {
            $updateData['translations'][] = [
                'languageId' => $this->selectedComparisonLanguage['id'],
                'value' => $this->editFormComparisonValue
            ];
        }

        Log::info("Attempting to save item ID: {$this->editingItemId} with data:", $updateData);

        // Call your API service to update the item
        // Replace 'updateInterfaceTranslationKey' with your actual service method
        // The method signature and data structure will depend on your API
        // $success = $this->apiService->updateInterfaceTranslationKey($this->editingItemId, $updateData);
        $success = true; // Placeholder for API call success

        if ($success) {
            Log::info("Item ID: {$this->editingItemId} updated successfully.");
            $this->dispatch('notify', message: 'Item updated successfully!', type: 'success');
            $this->closeEditModal();
            // $this->loadInterfaceKeys(); // Or let computed property re-fetch
            $this->resetPage(); // To refresh the current page of the table
        } else {
            Log::error("Failed to update item ID: {$this->editingItemId}.");
            $this->dispatch('notify', message: 'Error updating item. Please try again.', type: 'error');
        }
    }

    public function saveItemFromModal($itemId, $itemKey, $baseValue, $comparisonValue, $appId /*, ...other form fields */)
    {
        // Log::info("Livewire saveItemFromModal called for ID: {$itemId}", [
        //     'key' => $itemKey,
        //     'baseValue' => $baseValue,
        //     'comparisonValue' => $comparisonValue,
        //     'appId' => $appId
        // ]);

        // Basic validation (use Laravel validation for robust checks)
        if (empty($itemKey) || empty($baseValue)) { // Assuming key isn't really editable, but baseValue is
            $this->dispatch('notify', message: 'Base Value cannot be empty.', type: 'warning');
            return false; // Indicate failure to Alpine
        }

        $updatePayload = [
            'id' => $itemId,
            'itemKey' => $itemKey,
            'app_id' => $appId,
        ];

        if ($this->selectedLanguage && isset($this->selectedLanguage['id'])) {
            $updatePayload['selectedLanguage'] = [
                'language_id' => $this->selectedLanguage['id'],
                'value' => $baseValue,
            ];
        }
        if (
            $this->selectedComparisonLanguage && isset($this->selectedComparisonLanguage['id']) &&
            (!isset($this->selectedLanguage['id']) || $this->selectedComparisonLanguage['id'] != $this->selectedLanguage['id'])
        ) {
            $updatePayload['selectedComparisonLanguage'] = [
                'language_id' => $this->selectedComparisonLanguage['id'],
                'value' => $comparisonValue,
            ];
        }

        // Call your API service
        $success = $this->apiService->updateInterfaceTranslationKeyValues($updatePayload);
        // $success = true; // Placeholder

        if ($success) {
            $this->dispatch('notify', message: 'Item updated successfully!', type: 'success');
            $this->resetPage(); // Refresh table data
            return true; // Indicate success to Alpine
        } else {
            $this->dispatch('notify', message: 'Error updating item. Please try again.', type: 'error');
            return false; // Indicate failure to Alpine
        }
    }

    public function bulkCreateTranslations($items)
    {
        $created = 0;
        $errors = [];

        foreach ($items as $item) {
            // Basic validation
            if (empty($item['key']) || empty($item['en'])) {
                $errors[] = [
                    'key' => $item['key'] ?? null,
                    'error' => 'Missing key or English value'
                ];
                continue;
            }


            // You may want to check if the key already exists and update, or always create new
            // Example: $existing = TranslationKey::where('key', $item['key'])->first();

            // Example pseudo-code for your API/service:
            $payload = [
                'itemKey' => $item['key'],
                'app_id' => 0, // Or set from $item if you have it
                'values' => []
            ];

            // Add all language values dynamically
            foreach ($item as $lang => $value) {
                if ($lang === 'key' || $lang === 'app_id')
                    continue;
                // You need to map $lang (like 'en', 'fr') to your language IDs
                $languageId = $this->getLanguageIdByIso($lang);
                if ($languageId === 1) {
                    $payload['values']['base'] = [
                        'language_id' => $languageId,
                        'value' => $value,
                    ];
                } else {
                    $payload['values']['comparison'] = [
                        'language_id' => $languageId,
                        'value' => $value,
                    ];
                }
            }

            $success = true;

            $this->createNewItem(
                $payload['itemKey'],
                0,
                $payload['values']['base']['value'],
                $payload['values']['comparison']['value'],
                $payload['values']['base']['language_id'],
                $payload['values']['comparison']['language_id']
            );

            if ($success) {
                $created++;
            } else {
                $errors[] = [
                    'key' => $item['key'],
                    'error' => 'API error'
                ];
            }
        }

        if (count($errors) === 0) {
            $this->dispatch('notify', message: "All $created items created successfully!", type: 'success');
            $this->resetPage();
            return ['success' => true];
        } else {
            $this->dispatch('notify', message: "Some items failed.", type: 'warning');
            return ['success' => false, 'errors' => $errors];
        }
    }

    /**
     * Helper: Map ISO code to language ID
     */
    protected function getLanguageIdByIso($iso)
    {
        // Example: You may have a $this->languages array or query the DB
        $languages = [
            'en' => 1,
            'fr' => 2,
            // Add more as needed
        ];
        return $languages[$iso] ?? null;
    }

    // --- Create Modal Logic ---
    public function openCreateModal()
    {
        // This method is called by Alpine to ensure Livewire's form state is reset.
        Log::info('Livewire: openCreateModal called to reset form properties.');
        $this->resetCreateForm();
    }

    protected function resetCreateForm()
    {
        $this->reset([
            'createFormKey',
            'createFormBaseValue',
            'createFormComparisonValue',
        ]);
        $this->createFormAppId = 0; // Explicitly set default for app ID
        Log::info('Livewire: Create form properties reset.');
    }

    public function createNewItem($key, $appId, $baseValue, $comparisonValue, $languageId = 1, $translatedIntoLanguageId = 2)
    {
        // $validatedData = $this->validate([
        //     'createFormKey' => 'required|string|max:180',
        //     'createFormBaseValue' => 'required|string', // English value
        //     'createFormAppId' => 'required|integer|min:0',
        //     'createFormComparisonValue' => 'nullable|string', // Value for selectedLanguage
        // ]);

        $payload = [
            'key' => $key,
            'appId' => (int) $appId,
            'baseLanguageId' => $languageId,
            'baseValue' => $baseValue,
            'translatedIntoValue' => $comparisonValue,
            'translatedIntoLanguageId' => $translatedIntoLanguageId,
        ];

        Log::info('Attempting to create new item with payload:', $payload);


        $apiResponse = $this->apiService->createInterfaceTranslationKey($payload);

        if ($apiResponse['status']) {
            Log::info('New item created successfully.', ['response' => $apiResponse]);
            if (!isset($translatedIntoValue) && !isset($translatedIntoLanguageId)) {
                $this->dispatch('notify', message: 'New item created successfully!', type: 'success');
                $this->resetCreateForm(); // Reset Livewire's properties
            }
            return ['success' => true];
        } else {
            Notification::make()->danger()->title('Failed to create key.')->body($apiResponse['message'])->send();
            Log::error('Failed to create new item.', ['payload' => $payload, 'response' => $apiResponse ?? 'No API response']);
            $this->dispatch('notify', message: 'Error creating item. Please try again.', type: 'error');
            return ['success' => false, 'message' => $apiResponse['message'] ?? 'Failed to create item via API.'];
        }
    }

    public function applyAutoTranslation(array $payload)
    {
        $apiResponse = $this->apiService->useAutoTranslateInterfaceTranslation($payload);

        if ($apiResponse) {
            Log::info('New item created successfully.', ['response' => $apiResponse]);
            $this->resetPage();
            return ['success' => true];
        } else {
            Log::error('Failed to create new item.', ['payload' => $payload, 'response' => $apiResponse ?? 'No API response']);
            return ['success' => false, 'message' => $apiResponse['message'] ?? 'Failed to create item via API.'];
        }
    }

    public function bulkDelete(array $payload)
    {
        $successCount = 0;
        $errors = [];

        foreach ($payload as $keyId) {
            $apiResponse = $this->apiService->deleteTranslationKey($keyId);

            if ($apiResponse) {
                $successCount++;
            } else {
                $errors[] = [
                    'keyId' => $keyId,
                    'message' => $apiResponse['message'] ?? 'Failed to delete translation key via API.',
                ];
                Log::error('Bulk delete failed for key.', [
                    'keyId' => $keyId,
                    'response' => $apiResponse ?? 'No API response'
                ]);
            }
        }

        $this->resetPage();
        $this->updateLangFile();

        if (count($errors) === 0) {
            Notification::make()
                ->title('Bulk delete successful')
                ->body("All {$successCount} item(s) were deleted successfully.")
                ->success()
                ->send();
            return ['success' => true, 'message' => "All {$successCount} items deleted successfully."];
        }

        return [
            'success' => false,
            'message' => "Deleted {$successCount} item(s). " . count($errors) . " failed.",
            'errors' => $errors,
        ];
    }

    public function copyToClipboardNotif()
    {
        Notification::make()
            ->title('Copy successful.')
            ->body("All selected key has been copied to clipboard successfully.")
            ->success()
            ->send();
    }

    private function updateLangFile($appId = 0){
        try {
                $artisanStatus = Artisan::call('translations:init', ['--app' => (string) $appId, '--lang' => 'en']);
        } catch (\Throwable $e) {
            Log::error('translations:init command failed.', [
                'app_id' => $appId,
                'message' => $e->getMessage(),
            ]);
        }
    }
    public function apiCreateTranslation($payload)
    {
        $appId = $payload['appId'] ?? 0;
        $languageId = $payload['languageId'] ?? 1;
        $namespace = $payload['namespace'] ?? null;
        $jsonText = $payload['jsonText'] ?? '';

        try {
            json_decode($jsonText, true, 512, JSON_THROW_ON_ERROR);
        } catch (\Throwable $e) {
            return [
                'success' => false,
                'message' => 'Invalid JSON: ' . $e->getMessage(),
            ];
        }


        $dto = [
            'appId' => (int) $appId,
            'languageId' => (int) $languageId,
            'namespace' => $namespace,
            'jsonText' => $jsonText,
        ];

        $apiResponse = $this->apiService->postBatchCreateOrUpdate($dto);


        if (isset($apiResponse['success']) && $apiResponse['success']) {
            $this->dispatch('notify', message: "Batch created successfully!", type: 'success');
            Notification::make()
                ->title('Batch upload successful.')
                ->body("JSON uploaded successfully.")
                ->success()
                ->send();
            $this->resetPage();

            $this->updateLangFile($appId);

            return ['success' => true];
        } else {
            $message = $apiResponse['message'] ?? 'API error';
            $this->dispatch('notify', message: $message, type: 'error');
            return [
                'success' => false,
                'message' => $message,
                'errors' => $apiResponse['errors'] ?? [],
            ];
        }
    }
    private function array_dot($array, $prefix = '')
    {
        $results = [];
        foreach ($array as $key => $value) {
            $newKey = $prefix === '' ? $key : $prefix . '.' . $key;
            if (is_array($value)) {
                $results = array_merge($results, $this->array_dot($value, $newKey));
            } else {
                $results[$newKey] = $value;
            }
        }
        return $results;
    }

    private function extractFirstArgument(string $expression): string
    {
        $expression = trim($expression);
        $length = strlen($expression);
        $depth = 0;
        $quote = null;

        for ($i = 0; $i < $length; $i++) {
            $char = $expression[$i];

            if ($quote !== null) {
                if ($char === '\\' && $i + 1 < $length) {
                    $i++;
                    continue;
                }

                if ($char === $quote) {
                    $quote = null;
                }

                continue;
            }

            if ($char === '\'' || $char === '"') {
                $quote = $char;
                continue;
            }

            if ($char === '(' || $char === '[' || $char === '{') {
                $depth++;
                continue;
            }

            if ($char === ')' || $char === ']' || $char === '}') {
                if ($depth > 0) {
                    $depth--;
                }
                continue;
            }

            if ($char === ',' && $depth === 0) {
                return trim(substr($expression, 0, $i));
            }
        }

        return rtrim($expression, ', ');
    }

    public function apiScanKey($payload)
    {
        $appId = (int) $payload['appId'] ?? 0;

        $translations = [];
        if ($appId) {
            $translations = $this->array_dot(include lang_path("en/$appId.php"));
        } else {
            $translationFiles = glob(lang_path('en/*.php'));
            foreach ($translationFiles as $file) {
                $array = include $file;
                if (is_array($array)) {
                    $filename = basename($file, '.php');
                    $translations = array_merge(
                        $translations,
                        $this->array_dot($array)
                    );
                }
            }
        }

        $directory = new RecursiveDirectoryIterator(resource_path());

        $results = [];

        $excludeDirs = [
            'node_modules',
            'vendor',
            'storage',
            'public',
            'tests',
            'lang',
            'bootstrap',
            'database',
        ];

        $directory->setFlags(RecursiveDirectoryIterator::SKIP_DOTS);

        $excludeDirMap = array_flip($excludeDirs);

        $iterator = new RecursiveIteratorIterator(
            new RecursiveCallbackFilterIterator(
                $directory,
                static function ($current) use ($excludeDirMap) {
                    if ($current->isDir()) {
                        return !isset($excludeDirMap[$current->getFilename()]);
                    }

                    return true;
                }
            )
        );

        $pattern = '/(?:\bt|__)\s*\(\s*(.+?)\)/';

        foreach ($iterator as $file) {
            if ($file->isFile() && preg_match('/\.(php|js)$/', $file->getFilename())) {
                $lines = file($file->getPathname());
                foreach ($lines as $lineNumber => $lineContent) {
                    if (preg_match_all($pattern, $lineContent, $matches)) {
                        foreach ($matches[1] as $rawMatch) {
                            $firstArg = $this->extractFirstArgument($rawMatch);

                            if ($firstArg === '') {
                                continue;
                            }

                            $filePath = str_replace(
                                resource_path() . DIRECTORY_SEPARATOR,
                                'resources' . DIRECTORY_SEPARATOR,
                                $file->getPathname()
                            );

                            if (preg_match('~^([\'"])((?:\\.|(?!\1).)*)\1$~', $firstArg, $stringMatch)) {
                                $key = stripslashes($stringMatch[2]);

                                $isValidKey = (bool) preg_match('/^(?:[a-z0-9_]+\.)*[a-z0-9_]+$/', $key);

                                if ($isValidKey) {
                                    if (!array_key_exists($key, $translations)) {
                                        $results[] = [
                                            'key' => $key,
                                            'file' => $filePath,
                                            'line' => $lineNumber + 1,
                                            'type' => 'missing_key',
                                        ];
                                    }
                                } else {
                                    $results[] = [
                                        'key' => $key,
                                        'file' => $filePath,
                                        'line' => $lineNumber + 1,
                                        'type' => 'raw_string',
                                    ];
                                }

                                continue;
                            }

                            $results[] = [
                                'key' => $firstArg,
                                'file' => $filePath,
                                'line' => $lineNumber + 1,
                                'type' => 'code_expression',
                            ];
                        }
                    }
                }
            }
        }


        return [
            'success' => true,
            'translations' => count($translations),
            'appId' => $appId,
            'data' => $results,
            'total' => count($results),
        ];
    }

    public function deleteTranslationKey(int $keyId)
    {
        $apiResponse = $this->apiService->deleteTranslationKey($keyId);

        if ($apiResponse) {
            $this->resetPage();
            return ['success' => true];
        } else {
            Log::error('Failed to delete the translation key.', ['payload' => $keyId, 'response' => $apiResponse ?? 'No API response']);
            return ['success' => false, 'message' => $apiResponse['message'] ?? 'Failed to create item via API.'];
        }
    }

    public function updateTranslationKeyAndApp($interfaceTranslationKeyId, $newKey, $newAppId)
    {
        $payload = [
            'key' => $newKey,
            'appId' => (int) $newAppId,
            'id' => $interfaceTranslationKeyId,
        ];

        Log::info('Attempting to create new item with payload:', $payload);


        $apiResponse = $this->apiService->updateInterfaceTranslationKey($payload); // Actual API call
        $success = $apiResponse['success'];

        if ($success) {
            Log::info('New item created successfully.', ['response' => $apiResponse]);
            $this->resetPage();
            return ['success' => true]; // Signal Alpine
        } else {
            Log::error('Failed to create new item.', ['payload' => $payload, 'response' => $apiResponse ?? 'No API response']);
            return ['success' => false, 'message' => $apiResponse['message'] ?? 'Failed to create item via API.'];
        }
    }

    public function render()
    {
        $interfaceKeysData = $this->getInterfaceKeysProperty();
        $currentInterfaceKeys = $interfaceKeysData['data'] ?? [];

        // dd($interfaceKeysData);

        // Use the component ID in the event name for uniqueness if needed
        $this->dispatch("interface-keys-updated-{$this->getId()}", keys: $currentInterfaceKeys);


        return view('livewire.translation-interface-manager', [
            'interfaceKeys' => $interfaceKeysData['data'],
            'totalInterfaceKeys' => $interfaceKeysData['total'],
        ]);
    }

}
