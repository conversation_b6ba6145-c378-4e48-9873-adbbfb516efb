<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use App\Models\FeatureDescription;
use League\CommonMark\CommonMarkConverter;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms;
use Illuminate\Support\Facades\Http;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;

class FeatureDescriptionDrawer extends Component implements HasForms
{
    use InteractsWithForms;
    use WithFileUploads;

    public string $featureKey = 'testimony';
    public ?string $description = null;
    public bool $isEditing = false;
    public bool $showPreview = false;
    public $image = null;
    public ?string $initialDescription = null;
    public array $sessionUploadedPaths = [];

    protected $rules = [
        'description' => 'nullable|string',
    ];

    public function mount(string $featureKey = 'testimony'): void
    {
        $this->featureKey = $featureKey;
        $this->loadDescription();
        $this->form->fill([
            'description' => $this->description,
        ]);
    }

    public function loadDescription(): void
    {
        $record = FeatureDescription::firstOrCreate(
            ['key' => $this->featureKey],
            ['description' => null]
        );
        $this->description = $record->description;
    }

    public function toggleEdit(): void
    {
        $this->isEditing = ! $this->isEditing;
        if ($this->isEditing) {
            $this->form->fill([
                'description' => $this->description,
            ]);
            $this->showPreview = false;
            $this->initialDescription = $this->description;
            $this->sessionUploadedPaths = [];
        }
    }

    public function togglePreview(): void
    {
        $this->showPreview = ! $this->showPreview;
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $this->description = $data['description'] ?? $this->description;
        $this->validate();
        FeatureDescription::updateOrCreate(
            ['key' => $this->featureKey],
            ['description' => $this->description]
        );
        $this->isEditing = false;
        $this->dispatch('feature-description-saved');
        $this->initialDescription = null;
        $this->sessionUploadedPaths = [];
    }

    public function cancel(): void
    {
        $this->description = $this->initialDescription;
        $this->form->fill([
            'description' => $this->initialDescription,
        ]);
        $this->showPreview = false;
        $this->isEditing = false;
        // Do not delete uploaded images on cancel; just reset local state
        $this->sessionUploadedPaths = [];
        $this->image = null;
        $this->initialDescription = null;
    }

    public function getHtmlProperty(): string
    {
        $converter = new CommonMarkConverter();
        return $converter->convert($this->description ?? '')->getContent();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\MarkdownEditor::make('description')
                    ->label('')
                    ->toolbarButtons([
                        'bold',
                        'italic',
                        'strike',
                        'heading',
                        'bulletList',
                        'orderedList',
                        'blockquote',
                        'codeBlock',
                        'link',
                        'undo',
                        'redo',
                    ])
                    ->extraAttributes(['class' => 'min-h-[16rem]'])
                    ->columnSpanFull(),
            ]);
    }

    public function getPreviewHtmlProperty(): string
    {
        $current = $this->form->getState()['description'] ?? $this->description ?? '';
        $converter = new CommonMarkConverter();
        return $converter->convert($current)->getContent();
    }

    public function insertImage(string $url): void
    {
        $current = $this->form->getState()['description'] ?? '';
        $insertion = "\n\n![]($url)\n\n";
        $this->form->fill([
            'description' => $current . $insertion,
        ]);
    }

    public function insertUploadedImage(): void
    {
        if (!$this->image) {
            return;
        }

        $this->validate([
            'image' => 'image|max:4096',
        ]);

        $path = $this->image->store('feature-descriptions', 'public');
        $url = Storage::url($path);

        $this->insertImage($url);
        $this->sessionUploadedPaths[] = $path;
        $this->image = null;
    }

    public function updatedImage(): void
    {
        // Automatically process the image upload when a file is selected
        $this->insertUploadedImage();
    }
    public function render()
    {
        return view('livewire.admin.feature-description-drawer');
    }
}
