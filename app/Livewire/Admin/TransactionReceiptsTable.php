<?php

namespace App\Livewire\Admin;

use App\Models\Receipt;
use App\Models\Transaction;
use App\Services\InvoiceService;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;

class TransactionReceiptsTable extends Component implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;
    private InvoiceService $invoiceService;

    public Transaction $transaction;
    public function boot(InvoiceService $invoiceService)
    {
        $this->invoiceService = $invoiceService;
    }
    
    public function mount(Transaction $transaction): void
    {
        $this->transaction = $transaction;
    }

    public function render()
    {
        return view('livewire.admin.transaction-receipts-table');
    }
     
}
