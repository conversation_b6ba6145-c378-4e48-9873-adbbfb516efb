<?php

namespace App\Livewire\Admin;

use App\Models\Country;
use App\Models\Receipt;
use App\Models\Transaction;
use App\Services\InvoiceService;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;

class TransactionActionsColumn extends Component implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;
    private InvoiceService $invoiceService;

    public Transaction $transaction;
    public Receipt $receipt;
    public string $docType;
    public function boot(InvoiceService $invoiceService)
    {
        $this->invoiceService = $invoiceService;
    }
     

    public function mount(Transaction $transaction): void
    {
        $this->transaction = $transaction;
    }

    public function render()
    {
        return view('livewire.admin.transaction-actions-column');
    }
    public function viewAction(): Action 
    {
        return Action::make('view')
            ->label(t('core.admin.invoice_receipt.view_btn_label',[],2))
            ->icon('heroicon-o-eye') 
            ->action(function () { 
                $uuid = $this->docType == 'receipt' ? $this->receipt->uuid : $this->transaction->uuid; 
                redirect()->route('invoice_receipt.generate', ['docType' => $this->docType, 'uuid' => $uuid]);
            });

    }
    public function generateManageAction(): Action
    {
        $docType = $this->docType; 
        return Action::make('generateManage')
            ->icon('heroicon-s-cog')
            ->label(t('core.admin.invoice_receipt.modal_manage.'.$docType.'.view_manage_button_label',[],2))
            ->modal()
            ->modalHeading(t('core.admin.invoice_receipt.modal_manage.'.$docType.'.heading',[],2))
            ->modalDescription(t('core.admin.invoice_receipt.modal_manage.'.$docType.'.description',[],2))
            ->modalSubmitActionLabel(t('core.admin.invoice_receipt.modal_manage.'.$docType.'.submit_btn',[],2))
            ->mountUsing(function (Form $form, array $arguments) {
                // Logging arguments

                // Find receipt by UUID
                $transaction = null;
                if(isset($arguments['transaction_uuid']) && $arguments['transaction_uuid']){
                    $transaction = Transaction::with(['cart'])->where('uuid', $arguments['transaction_uuid'])->firstOrFail();
                }
                else if(isset($arguments['receipt_uuid']) && $arguments['receipt_uuid']){
                    $receipt = Receipt::where('uuid', $arguments['receipt_uuid'])->firstOrFail();
                    $transaction = Transaction::with(['cart'])->where('id', $receipt->transaction_id)->firstOrFail();
                } 
                $customerJson = $transaction->customer_json;


                // Get user data from the transaction's subscription  
                $form->fill([
                    'email' =>  $customerJson['email'] ?? '',
                    'displayed_name' =>  $this->invoiceService->getInvoiceCustomerName($customerJson),
                    'phone' => $customerJson['address']['phone'] ?? '',
                    'address_line_1' => $customerJson['address']['address_line_1'] ?? '',
                    'address_line_2' =>  $customerJson['address']['address_line_2'] ?? '',
                    'city' =>  $customerJson['address']['city'] ?? '',
                    'country' => isset($customerJson['address']['country']) ?   $customerJson['address']['country']['name'] : '',
                ]);
            })
            ->form([
                Forms\Components\Section::make(__('Contact Information'))
                    ->schema([
                        Forms\Components\TextInput::make('email')
                            ->label(t('core.admin.invoice_receipt.modal_manage.email',[],2))
                            ->email(),

                        Forms\Components\TextInput::make('displayed_name')
                            ->label(t('core.admin.invoice_receipt.modal_manage.displayed_name',[],2)),
 
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('Address Information'))
                    ->schema([
                        Forms\Components\TextInput::make('address_line_1')
                            ->label(t('core.admin.invoice_receipt.modal_manage.address_line_1',[],2)),

                        Forms\Components\TextInput::make('address_line_2')
                            ->label(t('core.admin.invoice_receipt.modal_manage.address_line_2',[],2)),

                        Forms\Components\TextInput::make('city')
                            ->label(t('core.admin.invoice_receipt.modal_manage.city',[],2)),

                        \Parfaitementweb\FilamentCountryField\Forms\Components\Country::make('country')
                            ->label(t('core.admin.invoice_receipt.modal_manage.country',[],2)),
                    ])
                    ->columns(2),
            ])
            ->action(function (array $data, array $arguments) use ($docType) {
                $uuid = $this->docType == 'receipt' ? $this->receipt->uuid : $this->transaction->uuid; 

                // Convert country code to country name
                if (isset($data['country'])) {
                    $country = Country::where('code_alpha_2', $data['country'])->first();
                    $data['country'] = $country ? $country->name : null;
                }

               // redirects to generate invoice with the form values as json in the query string
                return redirect()->route('invoice_receipt.generate', [
                    'docType' => $docType,
                    'uuid' =>  $uuid,
                    'customData' => json_encode($data)
                ]);
            });
    }
    
     

}
