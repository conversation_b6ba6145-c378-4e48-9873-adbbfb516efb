<?php

namespace App\Routes;

use App\Http\Controllers\Api\EmailTemplateController;
use Illuminate\Support\Facades\Route;

class EmailTemplateRoutes
{
    public static function define(): void
    {
        Route::prefix('email-templates')
            ->name('email-templates.')
            ->controller(EmailTemplateController::class)
            ->group(function (): void {
                Route::post('render', 'render')->name('render');
                Route::get('', 'index')->name('index');
                Route::post('', 'store')->name('store');
                Route::get('{email_template}', 'show')->name('show');
                Route::match(['put', 'patch'], '{email_template}', 'update')->name('update');
                Route::delete('{email_template}', 'destroy')->name('destroy');
            });
    }
}
