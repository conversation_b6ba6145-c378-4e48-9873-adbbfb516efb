<?php

namespace App\Services;

use App\Constants\AccountTypeEnum;
use App\Models\User;
use App\Models\AccountInformation;
use Illuminate\Support\Facades\Hash;

class UserService
{
    public function createUser(array $data): User
    {
        $rawFullName = trim((string) ($data['name'] ?? ''));
        $firstName = trim((string) ($data['first_name'] ?? $data['firstname'] ?? ''));
        $lastName = trim((string) ($data['last_name'] ?? ''));

        if ($firstName === '' && $rawFullName !== '') {
            $parts = preg_split('/\s+/', $rawFullName, 2);
            $firstName = trim($parts[0] ?? '');

            if ($lastName === '' && isset($parts[1])) {
                $lastName = trim($parts[1]);
            }
        }

        if ($lastName === '' && $rawFullName !== '') {
            $lastName = $rawFullName;
        }

        if ($lastName === '' && $firstName !== '') {
            $lastName = $firstName;
        }

        if ($firstName === '') {
            $firstName = $lastName !== '' ? $lastName : 'User';
        }

        if ($lastName === '') {
            $lastName = $firstName;
        }

        $user = User::create([
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'settings_json' => isset($data["settings_json"]) ? $data["settings_json"] : null,
        ]);

        AccountInformation::updateOrCreate(
            ['user_id' => $user->id],
            [
                'account_type_id' => isset($data['account_type']) ? AccountTypeEnum::fromName($data['account_type']) : 1,
                'company_name' => isset($data["company_name"]) ? $data["company_name"] : null,
            ]
        );

        return $user;
    }
}
