<?php

namespace App\Services;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TranslationApiService
{
    protected PendingRequest $httpClient;
    protected LanguageService $languageService;
    protected ?string $apiKey;

    public function __construct()
    {
        $baseUrl      = config('services.translation_api.url');
        $this->apiKey = config('services.translation_api.key');

        $this->httpClient = Http::baseUrl($baseUrl)
            ->acceptJson()
            ->timeout(120)          
            ->connectTimeout(60);  

        if ($this->apiKey) {
            $this->httpClient->withHeaders(['appKey' => $this->apiKey]);
        }
        $this->languageService = app(LanguageService::class);
    }

    public function getLanguages(array $queryParams = []): array
    {
        $defaults = [
            'order'             => 'ASC',
            'orderBy'           => 'createdAt',
            'limit'             => 10,
            'offset'            => 0,
            'where'             => '{}',
            'searchOption'      => 'EXACT',
            'searchCombination' => 'AND',
            'includeRelations'  => true,
        ];
        $response = $this->httpClient->get('languages', array_merge($defaults, $queryParams));
        return $response->json();
    }

    public function getLanguage(string $id): ?array
    {
        $response = $this->httpClient->get("languages/{$id}");
        if ($response->successful()) {
            return $response->json();
        }
        return null;
    }

    public function createLanguage(array $data): array
    {
        $response = $this->httpClient->post('languages', $data);
        return $response->json();
    }

    public function updateLanguage(string $id, array $data): array
    {
        $response = $this->httpClient->patch("languages/{$id}", $data);
        return $response->json();
    }

    public function deleteLanguage(string $id): bool
    {
        $response = $this->httpClient->delete("languages/{$id}");
        return $response->successful();
    }

    public function getInterfaceTranslationKeyStat()
    {
        $response = $this->httpClient->get("interface-translation-key-stats");
        if ($response->successful()) {
            return $response->json();
        }
        Log::error('Failed to fetch word count stats from API', ['response' => $response->json()]);
        return null;
    }

    public function getLanguageStat(int $languageId)
    {
        $response = $this->httpClient->get(
            sprintf("interface-translation-key/language-stats/%s", $languageId)
        );
        if ($response->successful()) {
            return $response->json();
        }
        Log::error('Failed to fetch word count stats from API', ['response' => $response->json()]);
        return null;
    }

    public function getInterfaceTranslationKey($language, $comparison_language, array $queryParams = [])
    {
        $defaults = [
            'order'            => 'DESC',
            'translatedFilter' => 'all',
        ];
        $response = $this->httpClient->get(
            sprintf("interface-translation-key/%s/%s", $language, $comparison_language),
            array_merge($defaults, $queryParams)
        );

        if ($response->successful()) {
            return $response->json();
        }
        Log::error('Failed to fetch word count stats from API', ['response' => $response->json()]);
        return null;
    }

    public function getAllLanguages(array $queryParams = []): array
    {
        $defaults = [
            'order'             => 'ASC',
            'orderBy'           => 'createdAt',
            'limit'             => 10,
            'offset'            => 0,
            'where'             => '{}',
            'searchOption'      => 'EXACT',
            'searchCombination' => 'AND',
            'includeRelations'  => true,
        ];
        $response = $this->httpClient->get('languages', array_merge($defaults, $queryParams));
        return $response->json();
    }
    public function updateInterfaceTranslationKeyValues(array $data)
    {
        Log::debug('updateInterfaceTranslationKeyValues', ['data' => $data]);
        if (isset($data['selectedLanguage']) && $data['selectedLanguage']['value']) {
            $response = $this->httpClient->post('interface-translation-value', [
                "appId" => $data['app_id'],
                "interfaceTranslationKey" => $data['itemKey'],
                "lang" => $data['selectedLanguage']['language_id'],
                "value" => $data['selectedLanguage']['value'],
            ]);

            if ($response->successful()) {
                return true;
            }
        }

        if (isset($data['selectedComparisonLanguage']) && $data['selectedComparisonLanguage']['value']) {
            $response = $this->httpClient->post('interface-translation-value', [
                "appId" => $data['app_id'],
                "interfaceTranslationKey" => $data['itemKey'],
                "lang" => $data['selectedComparisonLanguage']['language_id'],
                "value" => $data['selectedComparisonLanguage']['value'],
            ]);


            if ($response->successful()) {
                return true;
            }
        }

        // return $response->json();
        return false;
    }
    public function createInterfaceTranslationKey(array $data)
    {

        Log::debug('updateInterfaceTranslationKeyValues', ['data' => $data]);
        $response = $this->httpClient->post('interface-translation-key', [
            "appId" => $data['appId'],
            "baseLanguageId" => $data['baseLanguageId'],
            "englishValue" => $data['baseValue'],
            "key" => $data['key'],
            "translatedIntoLanguageId" => $data['translatedIntoLanguageId'] ?? null,
            "translatedIntoValue" => $data['translatedIntoValue'] ?? null,
        ]);

        Log::debug('response', ['response' => $response]);

        logger($response);
        
        if ($response->successful() && $response['raw']['affectedRows'] > 0) {
            return ["status" => true];
        }
        return ["status" => false, "message" => $response['message']];
    }

    public function updateInterfaceTranslationKey(array $data)
    {
        $response = $this->httpClient->post('interface-translation-key', [
            "id" => $data['id'],
            "key" => $data['key'],
            "appId" => $data['appId'],
            "baseValue" => null,
            "englishValue" => null,
            "translatedIntoValue" => null,
        ]);

        Log::info('response', ['response' => $response]);

        if ($response->successful()) {
            return [
                "success" => true,
                "message" => $response['affected']
            ];
        } else if ($response['statusCode'] === 400) {
            return [
                "success" => false,
                "message" => $response['message']
            ];
        } else {
            return [
                "success" => false
            ];
        }
    }

    public function useAutoTranslateInterfaceTranslation(array $interfaceTranslationValueIds)
    {
        Log::debug('useAutoTranslateInterfaceTranslation', ['data' => $interfaceTranslationValueIds]);
        $response = $this->httpClient->post('interface-translation-value/use-autotranslation', [
            "interfaceTranslationValueIds" => $interfaceTranslationValueIds,
        ]);

        if ($response->successful()) {
            return true;
        }
        return false;
    }

    public function deleteTranslationKey(int $keyId)
    {
        $response = $this->httpClient->delete(sprintf("interface-translation-key/%s", $keyId));

        if ($response->successful()) {
            return true;
        }
        return false;
    }

    public function cmsTranslationTranslate($language, $requestBody)
    {
        $response = $this->httpClient->post(sprintf("translation/translate/%s", $language), $requestBody);

        if ($response->successful()) {
            return $response;
        }
        return false;
    }

    public function getClient(): PendingRequest
    {
        return $this->httpClient;
    }

    public function saveResource(array $body)
    {
        $response = $this->httpClient->post('resources', $body);
        if ($response->successful()) {
            return $response->json();
        } else {
            return [];
        }
    }

    public function getResource($apiId, $tableName, $columnName, $rowId)
    {
        $response = $this->httpClient->get("resources/$apiId/$tableName/$columnName/$rowId");
        if ($response->successful()) {
            return $response->json();
        } else {
            return [];
        }
    }

    public function autoTranslate(array $body, $params = [])
    {
        if (count($params) > 0) {
            $selectedLang = implode(',', $params);
            return $this->httpClient->get(sprintf('resources/re-autotranslate-resource/%s/%s/%s/%s?selectedLang=%s', $body['apiId'], $body['tableName'], $body['rowId'], $body['columnName'], urlencode($selectedLang)));
        } else {
            return $this->httpClient->get(sprintf('resources/re-autotranslate-resource/%s/%s/%s/%s', $body['apiId'], $body['tableName'], $body['rowId'], $body['columnName']));
        }
    }

    public function translateEmailTemplateFields(array $fields, array $targetLanguageIso2s, string $sourceLanguageIso2 = 'en'): array
    {
        $translations = [];

        $targets = collect($targetLanguageIso2s)
            ->filter(fn ($iso) => is_string($iso) && trim($iso) !== '')
            ->map(fn ($iso) => strtolower($iso))
            ->unique()
            ->values();

        if ($targets->isEmpty()) {
            return [];
        }

        foreach ($fields as $field => $value) {
            if (! is_string($field)) {
                continue;
            }

            if ($value === null || $value === '') {
                continue;
            }

            $cacheKey = md5($sourceLanguageIso2.'|'.$field.'|'.(is_string($value) ? $value : json_encode($value)));

            static $responseCache = [];

            if (! array_key_exists($cacheKey, $responseCache)) {
                try {
                    $response = $this->httpClient->post(
                        sprintf('translation/translate-string/%s', $sourceLanguageIso2),
                        ['toTranslate' => $value]
                    );

                    if (! $response->successful()) {
                        Log::warning('Email template translation failed', [
                            'field' => $field,
                            'sourceLanguage' => $sourceLanguageIso2,
                            'status' => $response->status(),
                            'body' => $response->body(),
                        ]);

                        $responseCache[$cacheKey] = null;
                    } else {
                        $responseCache[$cacheKey] = $response->json();
                    }
                } catch (\Throwable $exception) {
                    Log::error('Email template translation threw exception', [
                        'message' => $exception->getMessage(),
                        'field' => $field,
                    ]);

                    $responseCache[$cacheKey] = null;
                }
            }

            $data = $responseCache[$cacheKey];

            if (! is_array($data)) {
                continue;
            }

            foreach ($targets as $targetIso) {
                if (! array_key_exists($targetIso, $data)) {
                    continue;
                }

                $translations[$field][$targetIso] = $data[$targetIso];
            }
        }

        return $translations;
    }

    function translateUri($uri, $lang)
    {
        $requestBody = [
            "url" => [urldecode($uri)]
        ];

        $response = $this->httpClient->post(sprintf("/translation/%s/translateUrl", $lang), $requestBody);

        $responseData = $response->json();

        if ($responseData[0]) {
            return $responseData[0];
        }
        return null;
    }

    public function getResourceTranslation($queryParams)
    {
        $response = $this->httpClient->get('resources/v2', $queryParams);
        if ($response->successful()) {
            return $response->json();
        } else {
            return [];
        }
    }

    public function getResourceTranslationV4($queryParams)
    {
        $response = $this->httpClient->get('resources/V4', $queryParams);
        if ($response->successful()) {
            return $response->json();
        } else {
            return [];
        }
    }

    public function getTranslateUrlForLang(string $url, string $lang = '')
    {
        if ($lang == '') {
            $response = $this->httpClient->get(sprintf('/translation/translateUrlWithLang2?url=%s&lang=%s', urlencode($url), $lang));
            return $response->json();
        } else {
            $response = $this->httpClient->get(sprintf('/translation/translateUrlWithLang2?url=%s&lang=%s', urlencode($url), $lang));
            return $response->json();
        }
    }

    public function getLangISO2FromIp($ip)
    {
        if ($ip === "127.0.0.1")
            return "en";

        try {
            $response = $this->httpClient->post("/languages/isoCode2", [
                "ip" => $ip
            ]);

            if ($response->successful()) {
                $ipInfo = $response->json();
                if(isset($ipInfo[0]) && isset($ipInfo[0]['languageISO2'])) {
                    return  $ipInfo[0]['languageISO2'] ?? "en";
                }
            }

            return "en";
        } catch (\Throwable $th) {
            return "en";
        }
        return "en";
    }

    public function useIpBasedLanguage($lang, $request) {
        $queryParams = $request->getQueryString();
        $uri = urldecode($request->getPathInfo());
        if(!isset($lang)) {
            $clientIp = getClientIpAddress();
            $lang = $this->getLangISO2FromIp($clientIp) ?? "en";
            $cleandUrl = removeTrailingSlash("/$lang$uri");
            $this->languageService->updateLanguage($lang);
            return [
                "url" => $cleandUrl . ($queryParams ? '?' . $queryParams : ''),
                "statusCode" => 301,
            ];
        }
        $lang = $lang ?? 'en';
        $this->languageService->updateLanguage($lang);
        return $lang;
    }

    public function postBatchCreateOrUpdate($dto)
    {
        $response = $this->httpClient->post('interface-translation-key/batch', $dto);

        if ($response->successful())
        {
            return ['success' => true, "data" => $response->json()];
        } else 
        {
            return ['success' => false, "data" => $response->json(), "message" => $response->json()['message']];
        }
    }
}
