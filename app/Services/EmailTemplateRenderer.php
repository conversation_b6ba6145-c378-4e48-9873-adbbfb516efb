<?php

namespace App\Services;

use App\Models\EmailTemplate;
use App\Models\Language;
use App\Support\LocaleDirection;
use Illuminate\Support\Str;
use RuntimeException;

class EmailTemplateRenderer
{
    private array $sharedTemplateCache = [];
    private array $aggregatedSections = [
        'head' => [],
        'style' => [],
        'script' => [],
        'foot' => [],
    ];
    private array $parentTemplateCache = [];

    public function render(EmailTemplate $template, array $payload = []): array
    {
        $this->sharedTemplateCache = [];
        $this->aggregatedSections = [
            'head' => [],
            'style' => [],
            'script' => [],
            'foot' => [],
        ];
        $this->parentTemplateCache = [];

        $variables = $this->prepareVariables($payload);
        [$langAttribute, $direction] = $this->determineLanguageAttributes($template, $variables);

        $variables['lang'] = $langAttribute;
        $variables['dir'] = $direction;
        $visitedKeys = array_values(array_filter([$template->key]));

        $sections = $this->renderTemplateSections($template, $variables, $visitedKeys);

        foreach (['head', 'style', 'foot', 'script'] as $section) {
            if (! empty($this->aggregatedSections[$section])) {
                $sections[$section] = ($sections[$section] ?? '') . implode('', $this->aggregatedSections[$section]);
            }
        }

        $sections['html'] = $this->composeHtml(
            $langAttribute,
            $direction,
            $sections['head'] ?? null,
            $sections['style'] ?? null,
            $sections['content'] ?? null,
            $sections['foot'] ?? null,
            $sections['script'] ?? null
        );

        return $sections;
    }

    private function renderTemplateSections(EmailTemplate $template, array $variables, array $visitedKeys): array
    {
        $headContent = $this->resolveSection($template, 'head');
        $contentTemplate = $this->resolveSection($template, 'content');
        $footContent = $this->resolveSection($template, 'foot');

        $head = $this->parseTemplate($headContent, $template, $variables, $visitedKeys);
        $content = $this->parseTemplate($contentTemplate, $template, $variables, $visitedKeys);
        $foot = $this->parseTemplate($footContent, $template, $variables, $visitedKeys);
        $styleSource = $this->resolveInheritedField($template, 'style');
        $scriptSource = $this->resolveInheritedField($template, 'script');

        $style = $this->parseTemplate($styleSource, $template, $variables, $visitedKeys);
        $script = $this->parseTemplate($scriptSource, $template, $variables, $visitedKeys);

        return [
            'head' => $head,
            'content' => $content,
            'foot' => $foot,
            'style' => $style,
            'script' => $script,
        ];
    }

    private function resolveSection(EmailTemplate $template, string $section): ?string
    {
        return $template->resolvedSection($section);
    }

    private function prepareVariables(array $payload): array
    {
        $defaults = [
            'app_name' => config('app.name'),
            'app_url' => config('app.url'),
        ];

        $merged = array_merge($defaults, $payload);

        $normalized = [];
        foreach ($merged as $key => $value) {
            $normalized[strtolower((string) $key)] = $this->stringifyValue($value);
        }

        return $normalized;
    }

    private function parseTemplate(?string $template, EmailTemplate $context, array $variables, array $visitedKeys): ?string
    {
        if ($template === null) {
            return null;
        }

        $templateWithSharedComponents = preg_replace_callback(
            '/{{{\s*(.+?)\s*}}}/',
            function (array $matches) use ($context, $variables, $visitedKeys) {
                $componentKey = trim($matches[1]);

                if ($componentKey === '') {
                    return $matches[0];
                }

                return $this->renderSharedComponent($componentKey, $context, $variables, $visitedKeys);
            },
            $template
        );

        return preg_replace_callback(
            '/{{\s*(?!{)(.+?)\s*}}/',
            function (array $matches) use ($variables) {
                $token = trim($matches[1]);
                $lookup = strtolower($token);

                if (array_key_exists($lookup, $variables)) {
                    return $variables[$lookup];
                }

                return $matches[0];
            },
            $templateWithSharedComponents
        );
    }

    private function composeHtml(string $lang, string $direction, ?string $head, ?string $style, ?string $content, ?string $foot, ?string $script): string
    {
        $headSection = $this->buildDocumentHead($style);
        $bodySection = $this->buildBodySection($head, $content, $foot, $script);

        $langAttr = htmlspecialchars(str_replace('_', '-', $lang), ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
        $dirAttr = htmlspecialchars($direction, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');

        return sprintf('<!DOCTYPE html><html lang="%s" dir="%s">%s%s</html>', $langAttr, $dirAttr, $headSection, $bodySection);
    }

    private function buildDocumentHead(?string $style): string
    {
        $styleTag = $this->formatStyle($style);

        return '<head>' . $styleTag . '</head>';
    }

    private function buildBodySection(?string $head, ?string $content, ?string $foot, ?string $script): string
    {
        $body = '';

        if ($head !== null) {
            $body .= $head;
        }

        if ($content !== null) {
            $body .= $content;
        }

        if ($foot !== null) {
            $body .= $foot;
        }

        $body .= $this->formatScript($script);

        return '<body>' . $body . '</body>';
    }

    private function formatStyle(?string $style): string
    {
        if ($style === null || trim($style) === '') {
            return '';
        }

        return Str::contains(Str::lower($style), '<style')
            ? $style
            : '<style>' . $style . '</style>';
    }

    private function formatScript(?string $script): string
    {
        if ($script === null || trim($script) === '') {
            return '';
        }

        return Str::contains(Str::lower($script), '<script')
            ? $script
            : '<script>' . $script . '</script>';
    }

    private function stringifyValue(mixed $value): string
    {
        if (is_scalar($value) || $value === null) {
            return (string) $value;
        }

        if (is_object($value) && method_exists($value, '__toString')) {
            return (string) $value;
        }

        return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    private function renderSharedComponent(string $componentKey, EmailTemplate $context, array $variables, array $visitedKeys): string
    {
        if (in_array($componentKey, $visitedKeys, true)) {
            throw new RuntimeException(sprintf('Circular shared component reference detected for key [%s].', $componentKey));
        }

        $template = $this->findSharedTemplateByKey($componentKey, $context->language_id);

        if ($template === null) {
            throw new RuntimeException(sprintf('Shared email template with key [%s] not found.', $componentKey));
        }

        if ($template->is_shared === false) {
            throw new RuntimeException(sprintf('Email template with key [%s] is not marked as shared.', $componentKey));
        }

        $nextVisited = array_merge($visitedKeys, array_filter([$context->key, $componentKey]));

        $componentSections = $this->renderTemplateSections(
            $template,
            $variables,
            array_values(array_unique($nextVisited))
        );

        $this->collectAggregatedSections($componentKey, $componentSections);

        return $componentSections['content'] ?? '';
    }

    private function findSharedTemplateByKey(string $key, ?int $languageId): ?EmailTemplate
    {
        $cacheKey = $this->buildSharedTemplateCacheKey($key, $languageId);

        if (! array_key_exists($cacheKey, $this->sharedTemplateCache)) {
            $template = null;

            if ($languageId !== null) {
                $template = EmailTemplate::findByKeyAndLanguage($key, $languageId);
            }

            $template = $this->resolveFallbackSharedTemplate($key, $template);

            $this->sharedTemplateCache[$cacheKey] = $template;
        }

        return $this->sharedTemplateCache[$cacheKey];
    }

    private function buildSharedTemplateCacheKey(string $key, ?int $languageId): string
    {
        return $key.'::'.($languageId ?? 'null');
    }

    private function resolveFallbackSharedTemplate(string $key, ?EmailTemplate $preferred): ?EmailTemplate
    {
        if ($preferred !== null && $preferred->is_shared) {
            return $preferred;
        }

        $fallback = EmailTemplate::query()
            ->where('key', $key)
            ->where('is_shared', true)
            ->orderByRaw('CASE WHEN id_row IS NULL THEN 0 ELSE 1 END')
            ->orderBy('id')
            ->first();

        if ($fallback !== null) {
            return $fallback;
        }

        return $preferred;
    }

    private function collectAggregatedSections(string $componentKey, array $sections): void
    {
        foreach (['head', 'style', 'foot', 'script'] as $section) {
            $value = $sections[$section] ?? null;

            if ($value === null || trim($value) === '') {
                continue;
            }

            if (! array_key_exists($componentKey, $this->aggregatedSections[$section])) {
                $this->aggregatedSections[$section][$componentKey] = $value;
            }
        }
    }

    private function resolveInheritedField(EmailTemplate $template, string $field, array $visited = []): ?string
    {
        $value = $template->getAttribute($field);

        if ($this->hasRenderableValue($value)) {
            return $value;
        }

        $parentId = $template->id_row;

        if ($parentId === null) {
            return $value;
        }

        if (in_array($parentId, $visited, true)) {
            return $value;
        }

        $parent = $this->findParentTemplate($parentId);

        if ($parent === null) {
            return $value;
        }

        $visited[] = $parentId;

        return $this->resolveInheritedField($parent, $field, $visited);
    }

    private function hasRenderableValue(mixed $value): bool
    {
        if ($value === null) {
            return false;
        }

        if (! is_string($value)) {
            return true;
        }

        return trim($value) !== '';
    }

    private function findParentTemplate(?int $parentId): ?EmailTemplate
    {
        if ($parentId === null) {
            return null;
        }

        if (! array_key_exists($parentId, $this->parentTemplateCache)) {
            $this->parentTemplateCache[$parentId] = EmailTemplate::query()->find($parentId);
        }

        return $this->parentTemplateCache[$parentId];
    }

    private function determineLanguageAttributes(EmailTemplate $template, array $variables): array
    {
        $preferredLocale = $template->locale;

        if ($preferredLocale === null || trim((string) $preferredLocale) === '') {
            $preferredLocale = $variables['lang'] ?? null;
        }
        $language = null;

        if ($template->language_id !== null) {
            $language = Language::query()->firstWhere('id', $template->language_id);
        }

        return LocaleDirection::resolve($preferredLocale, $language);
    }
}
