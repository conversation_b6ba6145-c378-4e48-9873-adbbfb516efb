<?php

namespace App\Services;

use App\Support\TwoFactor;
use Illuminate\Support\Facades\Auth;
use Laragear\TwoFactor\Facades\Auth2FA;

class LoginService
{
    public function attempt(array $credentials, bool $remember): bool
    {
        if (TwoFactor::isAvailable()) {
            return Auth2FA::attempt($credentials, $remember);
        }

        return Auth::guard()->attempt($credentials, $remember);
    }
}
