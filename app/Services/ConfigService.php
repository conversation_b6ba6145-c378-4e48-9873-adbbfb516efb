<?php

namespace App\Services;

use App\Constants\ConfigConstants;
use App\Models\Config;

class ConfigService
{
    public function isAdminSettingsEnabled()
    {
        return config('app.admin_settings.enabled') ?? false;
    }

    public function loadConfigs()
    {
        $configs = cache()->many(ConfigConstants::OVERRIDABLE_CONFIGS);
        $dbConfigs = [];

        if(!$configs["cache.db_configuration_loaded"]) {
            $dbConfigs = $this->loadMissingConfigsFromDatabase($configs);
        }
        
        $allConfigs = array_merge($configs, $dbConfigs);

        config($this->toKeyValueArray($allConfigs));
    }

    public function set(string $key, $value): void
    {
        if (! in_array($key, ConfigConstants::OVERRIDABLE_CONFIGS)) {
            throw new \Exception("Config key $key is not overridable");
        }

        Config::set($key, $value);

        cache()->forever($key, $value);

        config([$key => $value]);
    }

    public function exportAllConfigs(): void
    {
        $configs = Config::getAll();

        foreach ($configs as $key => $value) {
            cache()->forever($key, $value);
        }
    }

    /**
     * This is a one-time operation to encrypt sensitive configs to migrate non-encrypted sensitive configs to be encrypted.
     */
    public function encryptSensitiveConfigs()
    {
        foreach (ConfigConstants::ENCRYPTED_CONFIGS as $key) {
            $value = Config::get($key);
            if ($value) {
                Config::set($key, $value);
            }
        }
    }

    public function get(string $key, ?string $default = null): string|array|null
    {
        try {
            return Config::get($key) ?? config($key) ?? $default;
        } catch (\Exception $e) {
            return $default;
        }
    }

    private function loadMissingConfigsFromDatabase(array $cachedConfigs): array
    {
        $missingConfigs = [];
        
        // Find configs that are null in cache
        foreach ($cachedConfigs as $key => $value) {
            if (is_null($value)) {
                $dbValue = Config::get($key);
                if ($dbValue !== null) {
                    $missingConfigs[$key] = $dbValue;
                    cache()->forever($key, $dbValue);
                }
            }
        }

        cache()->forever("cache.db_configuration_loaded", True);
        
        return $missingConfigs;
    }

    private function toKeyValueArray($configs): array
    {
        $result = [];
        foreach ($configs as $key => $value) {
            if (is_null($value)) {
                continue;
            }

            $result[$key] = $value;
        }

        return $result;
    }
}
