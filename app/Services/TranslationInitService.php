<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\AppModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use RuntimeException;

final class TranslationInitService
{
    public function __construct(
        private string $translationUrl = '',
        private int $limit = 50,
        private string $outputBase = ''
    ) {
        $this->translationUrl = $this->translationUrl ?: config('services.translation_api.url', '');
        $this->limit          = $this->limit          ?: (int) config('translation.page_limit', 50);
        $this->outputBase     = base_path('lang');

        if ($this->translationUrl === '') {
            throw new RuntimeException('Missing TRANSLATION_API_URL');
        }
    }

    public function initTranslation($languages = [], $apps = []): array
    {
        if(count($apps) == 0)
            $apps = $this->fetchAllApps();

        if(count($languages) == 0)
            $languages = $this->fetchAllLanguages();

        if (empty($apps)) {
            throw new RuntimeException('No apps found');
        }
        if (empty($languages)) {
            throw new RuntimeException('No languages found');
        }

        File::ensureDirectoryExists($this->outputBase);

        $summary = ['written' => 0, 'errors' => []];

        foreach ($apps as $app) {
            $appId = (int) (Arr::get($app, 'id') ?? 0);
            if ($appId < 0) {
                Log::warning('App skipped: invalid id', ['app' => $app]);
                continue;
            }

            $base = rtrim($this->translationUrl, '/') . "/interface-translation-value/json/{$appId}";
            foreach ($languages as $lang) {
                $iso2 = (string) (Arr::get($lang, 'languageISO2') ?? Arr::get($lang, 'iso2') ?? '');
                if ($iso2 === '') {
                    Log::warning('Language skipped: missing ISO2', ['lang' => $lang]);
                    continue;
                }

                $url = $base . '/' . rawurlencode($iso2);
                try {
                    $payload = $this->httpGetJson($url);
                    $this->writeLangFile($iso2, $payload, $appId);
                    $summary['written']++;
                } catch (\Throwable $e) {
                    $msg = "Error app={$appId} iso={$iso2}: " . $e->getMessage();
                    Log::error($msg);
                    $summary['errors'][] = $msg;
                }
            }
        }

        return $summary;
    }

    /** @return array<int, array<string,mixed>> */
    private function fetchAllApps()
    {
        return AppModel::all();
    }

    /** @return array<int, array<string,mixed>> */
    private function fetchAllLanguages(): array
    {
        $url    = rtrim($this->translationUrl, '/') . '/languages';
        $offset = 0;
        $all    = [];

        while (true) {
            $resp = $this->httpGetJson($url, [
                'limit'   => $this->limit,
                'offset'  => $offset,
                'orderBy' => 'id',
                'order'   => 'ASC',
            ]);

            $chunk = Arr::get($resp, 'data', []);
            if (!is_array($chunk)) {
                throw new RuntimeException('Unexpected structure for /languages');
            }

            $all = array_merge($all, $chunk);

            $meta      = Arr::get($resp, 'meta', []);
            $itemCount = (int) (Arr::get($meta, 'itemCount', count($all)));
            if (count($all) >= $itemCount) {
                break;
            }
            $offset += $this->limit;
        }

        return $all;
    }

    /** @param array<string,mixed> $query */
    private function httpGetJson(string $url, array $query = []): array
    {
        $resp = Http::acceptJson()
            ->retry(3, 500)
            ->timeout(60)
            ->get($url, $query);

        if ($resp->failed()) {
            throw new RuntimeException("HTTP {$resp->status()} for {$url}");
        }

        $json = $resp->json();
        if (!is_array($json)) {
            throw new RuntimeException("Invalid JSON from {$url}");
        }
        return $json;
    }

    /** @param array<string,mixed> $payload */
    private function writeLangFile(string $iso2, array $payload, int $appId): void
    {
        $iso2 = strtolower(trim($iso2));
        $dir  = rtrim($this->outputBase, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $iso2;
        File::ensureDirectoryExists($dir);

        $data = array_key_exists('data', $payload) && is_array($payload['data'])
            ? $payload['data']
            : $payload;

        $php = "<?php\nreturn " . $this->exportShortArray($data) . ";\n";
        $file = $dir . DIRECTORY_SEPARATOR . "{$appId}.php";

        if (File::put($file, $php) === false) {
            throw new RuntimeException("Failed to write: {$file}");
        }
        Log::info('Language file written', ['iso' => $iso2, 'file' => $file]);
    }

    /** @param array<mixed> $data */
    private function exportShortArray(array $data): string
    {
        $export = var_export($data, true);
        $export = preg_replace("/^([ ]*)array \\(/m", '$1[', $export);
        $export = preg_replace("/\\)(,)?$/m", ']$1', $export);
        return $export ?? var_export($data, true);
    }
}
