<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\BaseModel;  

class Product extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'slug',
        'feature',
        'is_popular',
        'is_default',
        'metadata',
        'account_type',
        'credit_count'
    ];

    protected $casts = [
        'feature' => 'array',
        'metadata' => 'array',
    ];

    public function plans(): HasMany
    {
        return $this->hasMany(Plan::class);
    }

    public function features()
    {
        return $this->belongsToMany(Feature::class, 'feature_product')
                ->orderBy('ui_order')
                ->withPivot('enabled')
                ->withTimestamps();
    }
}
