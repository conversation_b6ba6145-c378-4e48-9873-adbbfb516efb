<?php

namespace App\Models;

use App\Constants\AgeRangeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccountInformation extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $table = 'account_informations';

    protected $fillable = [
        'user_id',
        'account_type_id',
        'account_category_id',
        'profession',
        'age_range',
        'company_name',
        'other_category',
    ];

    protected $casts = [
        'age_range' => AgeRangeEnum::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function accountCategory(): BelongsTo
    {
        return $this->belongsTo(AccountCategory::class);
    }

    public function accountType(): BelongsTo
    {
        return $this->belongsTo(AccountType::class);
    }
}
