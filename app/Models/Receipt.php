<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\BaseModel;  

class Receipt extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'transaction_id',
        'status',
        'filename',
        'payment_provider_object_id',
        'receipt_number',
        'amount_paid',
        'remaining_to_pay',
        'amount_already_paid',
        'receipt_status'

    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }
    public function saveReceiptNumber(){
        $this->receipt_number = Carbon::parse($this->created_at)->format('Y-m')
            .'-'
            . str_pad($this->id."", 4, "0", STR_PAD_LEFT);
        $this->save();
    }
}
