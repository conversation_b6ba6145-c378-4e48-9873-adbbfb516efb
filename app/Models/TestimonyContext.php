<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class TestimonyContext extends Model
{
    protected $fillable = [
        'name',
        'slug',
    ];

    protected static function booted(): void
    {
        static::creating(function (self $context) {
            if (empty($context->slug) && !empty($context->name)) {
                $context->slug = Str::slug($context->name);
            }
        });

        static::updating(function (self $context) {
            if (empty($context->slug) && !empty($context->name)) {
                $context->slug = Str::slug($context->name);
            }
        });
    }

    public function testimonies()
    {
        return $this->hasMany(Testimony::class, 'context_id');
    }
}

