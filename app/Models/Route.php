<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;  

class Route extends BaseModel
{
    use HasFactory;

    protected $table = 'routes';

    protected $fillable = [
        'appId',
        'pattern',
        'deleted',
    ];

    public function app()
    {
        return $this->belongsTo(AppModel::class, 'appId');
    }
}
