<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\SoftDeletes;

class AllowedRoute extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $table = 'allowed_routes';

    protected $fillable = [
        'roleAppId',
        'routeId',
        'methods',
        'deleted',
    ];

    protected $casts = [
        'deleted' => 'boolean',
        'methods' => 'array',
    ];

    public function roleApp()
    {
        return $this->belongsTo(RoleApp::class, 'roleAppId');
    }

    public function route()
    {
        return $this->belongsTo(Route::class, 'routeId');
    }
}
