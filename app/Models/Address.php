<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  

class Address extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id', 
        'country_id',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'zip',
        'country_code',
        'phone',
        'tax_number',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
}
