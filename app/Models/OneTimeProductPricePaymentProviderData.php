<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;  

class OneTimeProductPricePaymentProviderData extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'one_time_product_price_id',
        'payment_provider_id',
        'payment_provider_price_id',
    ];
}
