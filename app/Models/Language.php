<?php

namespace App\Models;

use App\Services\TranslationApiService;
use Illuminate\Database\Eloquent\Model; 
use Illuminate\Database\Eloquent\SoftDeletes;
use Sushi\Sushi;
use App\Models\BaseModel;  

class Language extends BaseModel {
    use Sushi;

    protected $schema = [
        'id' => 'integer', 
        'languageFull' => 'string',
        'languageFullNative' => 'string',
        'languageISO2' => 'string',
        'languageISO3' => 'string',
        'flagImage' => 'string',
        'isBaseLanguage' => 'boolean',
        'isPopularLanguage' => 'boolean',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime',
    ];

    protected $fillable = [
        'id',
        'languageFull',
        'languageFullNative',
        'languageISO2',
        'languageISO3',
        'flagImage',
        'flagCode', 
        'isBaseLanguage',
        'isPopularLanguage',
        'createdAt', 
        'updatedAt',
    ];

    public $timestamps = false;

    public function getRows()
    {
        $apiService = app(TranslationApiService::class);
        $languagesData = $apiService->getLanguages(['limit' => 1000]); 

        return $languagesData['data'] ?? $languagesData ?? [];
    }

    /**
     * Optional: If you want to clear Sushi's internal cache for this model.
     * Call this after a CUD operation on the API if redirects don't refresh data.
     */
    public static function bustSushiCache()
    {
        // static::$sushiCache = [];
        // Or more specific:
        // unset(static::$sushiCache[static::class]);
    }
}
