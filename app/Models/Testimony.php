<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\BaseModel;  
use Illuminate\Support\Facades\Schema;

class Testimony extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'country_id',
        'import_id',
        'name',
        'testimony',
        'language_iso_2',
        'is_default',
        'context_id',
    ];

    public static function getItemsByLanguage($languageIso2 = null, $context = 'global')
    {
        $query = Testimony::with(['country', 'context']);

        if ($context !== 'all') {
            $query->where(function ($q) use ($context) {
                $q->whereHas('context', function ($qc) use ($context) {
                    $qc->where('slug', $context)->orWhere('name', $context);
                });

                // Backward compatibility if legacy column still exists
                if (Schema::hasColumn('testimonies', 'context')) {
                    $q->orWhere('context', $context);
                }
            });
        }

        if ($languageIso2) {
            $languageQuery = clone $query;
            $items = $languageQuery->where('language_iso_2', $languageIso2)->limit(20)->get();
            if ($items->isNotEmpty()) {
                return $items;
            }
        }

        return $query->where('is_default', true)->limit(20)->get();
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function context()
    {
        return $this->belongsTo(TestimonyContext::class, 'context_id');
    }
}
