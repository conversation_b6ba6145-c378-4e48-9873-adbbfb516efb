<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\SoftDeletes;

class AppModel extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $table = 'app';

    protected $fillable = [
        'name',
        'appKey',
        'secret',
        'url',
    ];

    protected $hidden = [
        'secret',
    ];

    protected $casts = [
        // 'secret' => 'hashed',
    ];

    // public function setSecretAttribute($value)
    // {
    //     $this->attributes['secret'] = bcrypt($value);
    // }

    public function routes()
    {
        return $this->hasMany(\App\Models\Route::class, 'appId');
    }

    public function roleApps()
    {
        return $this->hasMany(\App\Models\RoleApp::class, 'appId');
    }
}
