<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\Factories\HasFactory;


class Region extends BaseModel
{

    use HasFactory;

    protected $fillable = [
        'name',
        'import_id',
        'translation',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    public function countries(): HasMany
    {
        return $this->hasMany(Country::class);
    }
}
