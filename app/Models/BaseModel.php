<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use OwenIt\Auditing\Auditable;
use Illuminate\Database\Eloquent\Relations\MorphMany;

abstract class BaseModel extends Model implements AuditableContract
{
    use Auditable;

    
    protected array $auditEvents          = ['created', 'updated', 'deleted', 'restored'];
    protected bool  $auditUseFillable     = true;
    protected array $auditSnapshot        = []; 
    protected array $auditExclude         = ['password', 'remember_token'];
    protected bool  $auditWithTimestamps  = false;

    
    public function generateTags(): array
    {
        return array_values(array_unique([
            config('app.env'),
            class_basename(static::class),
        ]));
    }

    
    public function transformAudit(array $data): array
    {
        $attrs = $this->resolveSnapshotAttributes();

        
        $rawOriginal   = method_exists($this, 'getRawOriginal') ? $this->getRawOriginal() : $this->getOriginal();
        $originalModel = $this->newInstance([], true);
        $originalModel->setRawAttributes($rawOriginal, true);
        $originalModel->syncOriginal();

        $old = [];
        $new = [];
        foreach ($attrs as $attr) {
            $old[$attr] = $originalModel->getAttribute($attr);
            $new[$attr] = $this->getAttribute($attr);
        }
        
        if (($data['event'] ?? null) === 'created') {
            foreach ($attrs as $attr) {
                $old[$attr] = null;
            }
        }

        
        $modelTag = class_basename(static::class);
        $namespaceKeys = function (array $arr) use ($modelTag): array {
            $out = [];
            foreach ($arr as $k => $v) {
                $out["{$modelTag}.{$k}"] = $v;
            }
            return $out;
        };

        $data['old_values'] = $namespaceKeys(Arr::only($old, $attrs));
        $data['new_values'] = $namespaceKeys(Arr::only($new, $attrs));

        
        $normalize = function ($value) use (&$normalize) {
            if ($value instanceof \DateTimeInterface) {
                return $value->format(\DateTime::ATOM);
            }

            if (is_string($value)) {
                $trim = trim($value);

                
                if ($trim === '' || preg_match('/^\d{4}-\d{2}-$/', $trim)) {
                    return null;
                }

                
                try {
                    if (str_contains($trim, '-') || str_contains($trim, ':')) {
                        return Carbon::parse($trim)->toAtomString();
                    }
                } catch (\Throwable $e) {
                    
                }
                return $trim;
            }

            if (is_bool($value)) {
                return $value; 
            }

            if (is_array($value)) {
                foreach ($value as $k => $v) {
                    $value[$k] = $normalize($v);
                }
                return $value;
            }

            return $value;
        };

        $data['old_values'] = $normalize($data['old_values']);
        $data['new_values'] = $normalize($data['new_values']);

        
        $existingTags = $data['tags'] ?? [];
        if (!is_array($existingTags)) {
            $existingTags = [$existingTags];
        }
        $data['tags'] = array_values(array_unique(array_merge(
            $existingTags,
            $this->generateTags()
        )));

        return $data;
    }

    
    protected function resolveSnapshotAttributes(): array
    {
        if (!empty($this->auditSnapshot)) {
            $attrs = $this->auditSnapshot;
        } else {
            if ($this->auditUseFillable) {
                $attrs = $this->getFillable();
                if (empty($attrs)) {
                    $attrs = array_keys($this->getAttributes());
                }
            } else {
                $attrs = array_keys($this->getAttributes());
            }
        }

        if ($this->auditWithTimestamps) {
            $attrs = array_values(array_unique(array_merge($attrs, [
                $this->getCreatedAtColumn(),
                $this->getUpdatedAtColumn(),
                method_exists($this, 'getDeletedAtColumn') ? $this->getDeletedAtColumn() : 'deleted_at',
            ])));
        }

        if (!empty($this->auditExclude)) {
            $attrs = array_values(array_diff($attrs, $this->auditExclude));
        }

        return $attrs;
    }

    
    public function audits(): MorphMany
    {
        return $this->morphMany(\App\Models\Audit::class, 'auditable')->latest();
    }
}
