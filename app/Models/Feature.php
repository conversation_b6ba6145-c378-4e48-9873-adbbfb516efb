<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Feature extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'key',
        'ui_order',
        'display_for_free',
        'allow_for_free',
        'hide',
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class)
            ->withPivot('enabled') 
            ->withTimestamps();
    }
}
