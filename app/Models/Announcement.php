<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  

class Announcement extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'content',
        'starts_at',
        'ends_at',
        'is_active',
        'is_dismissible',
        'show_for_customers',
        'show_on_frontend',
        'show_on_user_dashboard',
    ];
}
