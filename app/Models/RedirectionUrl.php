<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use App\Services\CmsService;
use Deployer\Component\PharUpdate\Version\Builder;
use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;
use App\Models\BaseModel;  

class RedirectionUrl extends BaseModel
{
    use Sushi;

    protected $schema = [
        'id' => 'integer',
        'cmsId' => 'integer',
        'key' => 'string',
        'oldUrl' => 'string',
        'newUrl' => 'string',
        'flagCode' => 'string',
        'languageId' => 'string',
        'languageISO2' => 'string',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime',
    ];

    protected $fillable = [
        'id',
        'cmsId',
        'key',
        'oldUrl',
        'newUrl',
        'flagCode',
        'languageId',
        'languageISO2',
        'createdAt',
        'updatedAt',
    ];

    public $timestamps = false;

    private function normalize(array $item): array
    {
        return collect($item)->mapWithKeys(function ($value, $key) {
            if (is_array($value) || is_object($value)) {
                return [$key => json_encode($value)];
            }

            return [$key => $value];
        })->toArray();
    }

    public function getRows()
    {
        $cmsService = app(CmsService::class);
        $redirectionData = $cmsService->getListCMSRedirectionUrl();
        $rows = [];
        if (!is_array($redirectionData)) {
            return [];
        }
        if (isset($redirectionData['data']) && is_array($redirectionData['data'])) {
            $rows = $redirectionData['data'];
        }

        $rows = collect($rows)
            ->filter(fn($row) => empty($row['deletedAt']) || is_null($row['deletedAt']))
            ->map(function ($row) {
                return $this->normalize($row);
            })
            ->toArray();
        return $rows;
    }

    public static function bustSushiCache()
    {
        // static::$sushiCache = [];
        // Or more specific:
        // unset(static::$sushiCache[static::class]);
    }
}
