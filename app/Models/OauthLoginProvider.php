<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;  

class OauthLoginProvider extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'provider_name',
        'name',
        'client_id',
        'client_secret',
        'enabled',
    ];
}
