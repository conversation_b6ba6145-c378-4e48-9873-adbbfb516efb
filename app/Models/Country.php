<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Country extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code_alpha_2',
        'code_alpha_3',
        'timezone_offset',
        'normalized_name',
        'import_id',
        'status',
        'region_id',
        'base_languages',
        'default_language',
    ];

    protected $casts = [
        'base_languages' => 'array',
    ];

    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }
}
