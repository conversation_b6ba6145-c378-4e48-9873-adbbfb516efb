<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  

class RoleApp extends BaseModel
{
    use HasFactory;

    protected $table = 'role_apps';

    protected $fillable = [
        'appId',
        'role',
        'default',
        'deleted',
    ];

    protected $casts = [
        'default' => 'boolean',
        'deleted' => 'boolean',
    ];

    public function app()
    {
        return $this->belongsTo(AppModel::class, 'appId');
    }
}
