<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;  

class PlanPricePaymentProviderData extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'plan_price_id',
        'payment_provider_id',
        'payment_provider_price_id',
        'type',
    ];
}
