<?php
namespace App\Models;

use App\Services\TranslationApiService;
use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TranslationInterface extends BaseModel
{
    use HasFactory, Sushi;

    public function getRows()
    {
        $apiService    = app(TranslationApiService::class);
        $languagesData = $apiService->getAllLanguages(['limit' => 1000]);

        return $languagesData['data'] ?? $languagesData ?? [];
    }

    /**
     * Optional: If you want to clear <PERSON><PERSON>'s internal cache for this model.
     * Call this after a CUD operation on the API if redirects don't refresh data.
     */
    public static function bustSushiCache()
    {
        // static::$sushiCache = [];
        // Or more specific:
        // unset(static::$sushiCache[static::class]);
    }
}
