<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\AccountInformation;

class AccountCategory extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'account_type_id',
        'name',
        'is_other',
    ];

    protected $casts = [
        'is_other' => 'boolean',
    ];

    public function accountType(): BelongsTo
    {
        return $this->belongsTo(AccountType::class);
    }

    public function accountInformations(): HasMany
    {
        return $this->hasMany(AccountInformation::class);
    }
}
