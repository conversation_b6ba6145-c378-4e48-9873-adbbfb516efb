<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;
use Illuminate\Support\Arr;
use Elastic\Elasticsearch\ClientBuilder;
use Illuminate\Support\Facades\Cache;

class EsAudit extends Model
{
    use Sushi;

    protected $table = 'es_audits';

    protected $primaryKey = 'id';
    public $incrementing  = false;
    protected $keyType    = 'string';
    public $timestamps    = false;
    protected $casts      = [
        'created_at' => 'datetime',
    ];

    protected $schema = [
        'id'              => 'string',
        'event'           => 'string',
        'auditable_type'  => 'string',
        'auditable_id'    => 'integer',
        'user_name'       => 'string',
        'user_email'      => 'string',
        'user_type'       => 'string',
        'user_id'         => 'integer',
        'tags'            => 'string',
        'ip_address'      => 'string',
        'url'             => 'string',
        'created_at'      => 'datetime',
        'old_values_json' => 'text',
        'new_values_json' => 'text',
    ];

    protected function makeClient()
    {
        $hosts = config('auditing.drivers.elastic.hosts', [env('AUDIT_HOST', 'http://127.0.0.1:9200')]);
        $user  = env('ELASTIC_AUDIT_USER');
        $pass  = env('ELASTIC_AUDIT_PASSWORD');

        $builder = ClientBuilder::create()->setHosts(is_array($hosts) ? $hosts : [$hosts]);

        if (!empty($user) && !empty($pass)) {
            $builder->setBasicAuthentication($user, $pass);
        }

        return $builder->build();
    }

    public function getRows()
    {
        $limit    = (int) config('auditing.feeds.limit', 5000);
        $cacheTtl = (int) config('auditing.feeds.cache_ttl', 30);

        return Cache::remember("es_audits_rows_all_{$limit}", $cacheTtl, function () use ($limit) {
            $index  = config('auditing.drivers.elastic.index', 'laravel_auditing');
            $client = $this->makeClient();

            $params = [
                'index' => $index,
                'body'  => [
                    'query' => ['match_all' => (object)[]],
                    'sort'  => [['created_at' => ['order' => 'desc', 'unmapped_type' => 'date', 'missing' => '_last']]],
                    'size'  => $limit,
                ],
            ];

            try {
                $result = $client->search($params);
            } catch (\Throwable $e) {
                \Log::error('ES audits search failed', ['error' => $e->getMessage()]);
                return [];
            }

            // Log full result for debugging
            // \Log::info('ES audits search response', $result->asArray());

            $hits = data_get($result, 'hits.hits', []);

            return array_map(function ($hit) {
                $src  = $hit['_source'] ?? [];
                $tags = collect(Arr::wrap($src['tags'] ?? []))
                    ->flatMap(fn ($t) => is_string($t) ? explode(',', $t) : [$t])
                    ->map(fn ($v) => trim((string) $v))
                    ->filter()
                    ->unique()
                    ->implode(',');

                return [
                    'id'              => $hit['_id'] ?? null,
                    'event'           => $src['event'] ?? null,
                    'auditable_type'  => $src['auditable_type'] ?? null,
                    'auditable_id'    => $src['auditable_id'] ?? null,
                    'user_id'         => $src['user_id'] ?? null,
                    'user_type'       => $src['user_type'] ?? null,
                    'tags'            => $tags,
                    'ip_address'      => $src['ip_address'] ?? null,
                    'url'             => $src['url'] ?? null,
                    'created_at'      => $src['created_at'] ?? null,
                    'old_values' => json_encode($src['old_values'] ?? [], JSON_UNESCAPED_UNICODE),
                    'new_values' => json_encode($src['new_values'] ?? [], JSON_UNESCAPED_UNICODE),
                ];
            }, $hits);
        });
    }

    public static function auditableTypeOptions(): array
    {
        return Cache::remember('es_audits_auditable_type_options', 60, function () {
            $index  = config('auditing.drivers.elastic.index', 'laravel_auditing');
            $client = (new static)->makeClient();

            try {
                $res = $client->search([
                    'index' => $index,
                    'body'  => [
                        'size' => 0,
                        'aggs' => [
                            'types' => [
                                'terms' => ['field' => 'auditable_type.keyword', 'size' => 200],
                            ],
                        ],
                    ],
                ]);
            } catch (\Throwable $e) {
                \Log::error('ES audits types agg failed', ['error' => $e->getMessage()]);
                return [];
            }

            $buckets = data_get($res, 'aggregations.types.buckets', []);
            $opts    = [];
            foreach ($buckets as $b) {
                $key = $b['key'] ?? null;
                if (!$key) continue;
                $opts[$key] = class_basename($key);
            }
            asort($opts);
            return $opts;
        });
    }

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id');
    }
}
