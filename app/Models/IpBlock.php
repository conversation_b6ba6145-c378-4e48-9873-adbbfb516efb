<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\Factories\HasFactory;


class IpBlock extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'country_id',
        'cidr',
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
}
