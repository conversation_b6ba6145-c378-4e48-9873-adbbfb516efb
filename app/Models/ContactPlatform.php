<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  

class ContactPlatform extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'key',
        'label',
        'type',
    ];

    public function contacts(): HasMany
    {
        return $this->hasMany(UserContactPlatformInformation::class);
    }
}
