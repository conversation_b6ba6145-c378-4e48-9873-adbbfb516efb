<?php

namespace App\Models;

use Spatie\Permission\Models\Permission as SpatiePermission;

class Permission extends SpatiePermission
{
    public function setNameAttribute($value): void
    {
        if ($value === null) {
            $this->attributes['name'] = null;

            return;
        }

        $normalized = preg_replace('/\s+/', '-', trim((string) $value));
        $this->attributes['name'] = $normalized;
    }
}
