<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\AccountCategory;
use App\Models\AccountInformation;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;  

class AccountType extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'key',
        'name',
    ];

    public function categories(): HasMany
    {
        return $this->hasMany(AccountCategory::class);
    }

    public function accountInformations(): HasMany
    {
        return $this->hasMany(AccountInformation::class);
    }
}
