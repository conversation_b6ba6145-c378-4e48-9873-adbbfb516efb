<?php

namespace App\Models;

use App\Constants\TemplateStatus;
use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use RuntimeException;

class EmailTemplate extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'key',
        'name',
        'description',
        'language_id',
        'locale',
        'status',
        'is_shared',
        'head',
        'content',
        'foot',
        'style',
        'script',
        'created_by',
        'metadata',
        'id_row',
    ];

    protected $casts = [
        'status' => TemplateStatus::class,
        'language_id' => 'int',
        'is_shared' => 'bool',
        'id_row' => 'int',
    ];

    protected static function booted(): void
    {
        static::creating(static function (self $template): void {
            if ($template->created_by === null) {
                $template->created_by = Auth::id();
            }
        });

        static::saving(static function (self $template): void {
            $language = $template->language_id !== null
                ? static::resolveLanguage($template->language_id)
                : null;

            $isBaseLanguage = static::isBaseLanguage($language);
            $languageIso = $language !== null
                ? strtolower((string) ($language['languageISO2'] ?? ''))
                : null;

            if ($template->status === null && $isBaseLanguage) {
                $template->status = TemplateStatus::DRAFT;
            }

            if (! $isBaseLanguage) {
                $template->status = null;
            }

            if ($languageIso !== null && $languageIso !== '') {
                $template->locale = $languageIso;
            }

            if (! $isBaseLanguage) {
                $template->key = null;
            }
        });
    }

    protected static function isBaseLanguage(?array $language): bool
    {
        if ($language === null) {
            return false;
        }

        if (array_key_exists('isBaseLanguage', $language)) {
            return (bool) $language['isBaseLanguage'];
        }

        $iso = strtolower((string) ($language['languageISO2'] ?? ''));

        return $iso === 'en';
    }

    public function languageLabel(): Attribute
    {
        return Attribute::get(function (): ?string {
            if ($this->language_id === null) {
                return null;
            }

            $language = static::resolveLanguage($this->language_id);

            return $language['languageFull'] ?? null;
        });
    }

    public function languageDetails(): Attribute
    {
        return Attribute::get(function (): ?array {
            if ($this->language_id === null) {
                return null;
            }

            return static::resolveLanguage($this->language_id);
        });
    }

    protected static function resolveLanguage(?int $languageId): ?array
    {
        if ($languageId === null) {
            return null;
        }

        static $cache = [];

        if (!array_key_exists($languageId, $cache)) {
            /** @var Language|null $language */
            $language = Language::query()->firstWhere('id', $languageId);
            $cache[$languageId] = $language?->toArray();
        }

        return $cache[$languageId] ?? null;
    }

    public function scopeForKeyAndLanguage($query, string $key, int $languageId)
    {
        return $query
            ->where(function ($builder) use ($key) {
                $builder->where('key', $key)
                    ->orWhereHas('mother', function ($relation) use ($key) {
                        $relation->where('key', $key);
                    });
            })
            ->where('language_id', $languageId);
    }

    public static function findByKeyAndLanguage(string $key, int $languageId): ?self
    {
        return static::query()
            ->forKeyAndLanguage($key, $languageId)
            ->first();
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function mother(): BelongsTo
    {
        return $this->belongsTo(self::class, 'id_row');
    }

    public function resolvedSection(string $section, array $visitedKeys = []): ?string
    {
        $section = strtolower($section);

        if (!in_array($section, ['head', 'content', 'foot'], true)) {
            throw new RuntimeException(sprintf('Unsupported email template section [%s].', $section));
        }

        $value = $this->getAttribute($section);

        if ($value === null) {
            return null;
        }

        $trimmed = is_string($value) ? trim($value) : $value;

        return $trimmed === '' ? null : $value;
    }

}
