<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\BaseModel;  
use Illuminate\Database\Eloquent\SoftDeletes;

class StripeEvent extends BaseModel
{
   use HasFactory;

     /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
       'event_id',
       'event_type',
    ];



}
