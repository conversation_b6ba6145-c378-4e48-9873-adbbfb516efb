<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;  

class DiscountPaymentProviderData extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'discount_id',
        'payment_provider_id',
        'payment_provider_discount_id',
    ];
}
