<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;  

class SubscriptionDiscount extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'discount_id',
        'subscription_id',
        'is_recurring',
        'type',
        'amount',
        'valid_until',
    ];
}
