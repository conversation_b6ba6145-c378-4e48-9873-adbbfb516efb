<?php

namespace App\Support;

use App\Models\Language;

class LocaleDirection
{
    /**
     * ISO codes for languages that are typically rendered right-to-left.
     * Includes both ISO 639-1 and ISO 639-2/3 identifiers.
     */
    private const RTL_LANGUAGE_CODES = [
        'adlm',
        'ae',
        'aeb',
        'ar',
        'ara',
        'arb',
        'arq',
        'ars',
        'ary',
        'arz',
        'avl',
        'ckb',
        'cku',
        'ckz',
        'dv',
        'div',
        'fa',
        'fas',
        'glk',
        'ha',
        'hau',
        'he',
        'heb',
        'iw',
        'ku',
        'kur',
        'ks',
        'kas',
        'mzn',
        'nqo',
        'ota',
        'pal',
        'peo',
        'pes',
        'prs',
        'ps',
        'pus',
        'sam',
        'sd',
        'snd',
        'syr',
        'syc',
        'tmr',
        'ug',
        'uig',
        'ur',
        'urd',
        'yi',
        'yid',
    ];

    public static function resolve(?string $explicitLocale, ?Language $language = null): array
    {
        $fallback = static::normalizeLocale(config('app.locale', 'en')) ?? 'en';
        $explicit = static::normalizeLocale($explicitLocale);
        $iso2 = static::normalizeLocale($language?->languageISO2 ?? null);
        $iso3 = static::normalizeLocale($language?->languageISO3 ?? null);

        $locale = $explicit;

        $shouldPreferLanguage = $locale === null || $locale === $fallback;
        if ($shouldPreferLanguage) {
            $locale = $iso2 ?? $iso3 ?? $locale;
        }

        if ($locale === null) {
            $locale = $iso2 ?? $iso3 ?? $fallback;
        }

        $direction = static::isRtlLocale($locale, $language) ? 'rtl' : 'ltr';

        return [$locale, $direction];
    }

    public static function isRtlLocale(?string $locale, ?Language $language = null): bool
    {
        $candidates = [
            static::normalizeLocale($locale),
            static::normalizeLocale($language?->languageISO2 ?? null),
            static::normalizeLocale($language?->languageISO3 ?? null),
        ];

        $rtlList = static::rtlCodes();

        foreach ($candidates as $candidate) {
            if ($candidate === null) {
                continue;
            }

            $parts = explode('-', $candidate);

            foreach ($parts as $part) {
                if ($part !== '' && in_array($part, $rtlList, true)) {
                    return true;
                }
            }

            if (in_array($candidate, $rtlList, true)) {
                return true;
            }
        }

        return false;
    }

    public static function rtlCodes(): array
    {
        return array_values(array_unique(static::RTL_LANGUAGE_CODES));
    }

    private static function normalizeLocale(?string $locale): ?string
    {
        if ($locale === null) {
            return null;
        }

        $trimmed = trim((string) $locale);

        if ($trimmed === '') {
            return null;
        }

        $normalized = str_replace(['_', ' '], '-', $trimmed);

        return strtolower($normalized);
    }
}
