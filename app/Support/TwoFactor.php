<?php

namespace App\Support;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;
use Laragear\TwoFactor\Models\TwoFactorAuthentication;

class TwoFactor
{
    public static function isEnabled(): bool
    {
        $value = Config::get('app.two_factor_auth_enabled', false);

        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            $filtered = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);

            if ($filtered !== null) {
                return $filtered;
            }
        }

        return (bool) $value;
    }

    public static function isDatabaseReady(): bool
    {
        try {
            $table = app(TwoFactorAuthentication::class)->getTable();

            return Schema::hasTable($table);
        } catch (\Throwable $exception) {
            return false;
        }
    }

    public static function isAvailable(): bool
    {
        return static::isEnabled() && static::isDatabaseReady();
    }
}
