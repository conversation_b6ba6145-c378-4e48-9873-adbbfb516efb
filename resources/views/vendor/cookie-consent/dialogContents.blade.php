@php
    $cookieBannerClasses = 'js-cookie-consent cookie-consent fixed bottom-0 inset-x-0 z-50 border-t border-gray-200 bg-white shadow-[0_-8px_16px_-12px_rgba(15,23,42,0.4)]';
    if (!empty($alreadyConsentedWithCookies)) {
        $cookieBannerClasses .= ' hidden';
    }
@endphp

<div class="{{ $cookieBannerClasses }}" data-cookie-consent-banner>
    <div class="max-w-6xl mx-auto px-6 py-6">
        <div class="flex flex-col gap-6 md:flex-row md:items-center md:justify-between">
            <div class="max-w-3xl space-y-2 text-slate-700">
                <h2 class="text-lg font-semibold text-slate-900">
                    Cookies
                </h2>
                <p class="text-sm leading-relaxed md:text-base cookie-consent__message">
                    {{ t('core.cookies.description') }}
                </p>
            </div>
            <div class="flex w-full flex-col gap-2 md:w-auto md:min-w-[300px]">
                <button type="button" class="js-cookie-consent-preferences cursor-pointer rounded-md bg-slate-100 px-4 py-2 text-sm font-medium text-slate-800 transition hover:bg-slate-200">
                    {{ t('core.cookies.manage_preferences') }}
                </button>
                <div class="flex w-full flex-col gap-2 md:flex-row">
                    <button type="button" class="js-cookie-consent-decline cursor-pointer rounded-md bg-slate-100 px-4 py-2 text-sm font-medium text-slate-700 transition hover:bg-slate-200 md:flex-1">
                        {{ t('core.cookies.reject_all') }}
                    </button>
                    <button type="button" class="js-cookie-consent-accept cookie-consent__agree cursor-pointer rounded-md bg-slate-900 px-4 py-2 text-sm font-semibold text-white transition hover:bg-slate-700 md:flex-1">
                        {{ t('core.cookies.accept_all') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="cookie-consent-modal pointer-events-none fixed inset-0 z-[60] hidden" data-cookie-consent-modal>
    <div class="absolute inset-0 bg-slate-900/60 opacity-0 transition-opacity" data-cookie-consent-modal-overlay></div>
    <div class="relative flex min-h-full items-end justify-center px-4 pb-6 pt-12 sm:items-center sm:p-6">
        <div class="w-full max-w-2xl translate-y-6 scale-95 transform rounded-xl bg-white p-6 text-slate-700 shadow-xl ring-1 ring-black/5 transition-all sm:translate-y-0 sm:scale-100" data-cookie-consent-modal-panel>
            <div class="flex items-start justify-between gap-4">
                <div>
                    <h2 class="text-xl font-semibold text-slate-900">
                        Cookies
                    </h2>
                    <p class="mt-2 text-sm leading-relaxed">
                        {{ t('core.cookies.modal_description') }}
                    </p>
                </div>
                <button type="button" class="rounded-full p-1 text-slate-500 transition hover:bg-slate-100 hover:text-slate-700" data-cookie-consent-modal-close>
                    <span class="sr-only">{{ t('core.cookies.modal_close_label') }}</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>

            <div class="mt-6 space-y-4">
                <div class="rounded-lg border border-slate-200 p-4">
                    <div class="flex items-start justify-between gap-4">
                        <div>
                            <h3 class="text-sm font-semibold text-slate-900">{{ t('core.cookies.necessary_title') }}</h3>
                            <p class="mt-1 text-xs leading-relaxed text-slate-500">
                                {{ t('core.cookies.necessary_description') }}
                            </p>
                        </div>
                        <span class="rounded-full bg-slate-100 px-3 py-1 text-xs font-medium text-slate-600">{{ t('core.cookies.always_active') }}</span>
                    </div>
                </div>

                <div class="rounded-lg border border-slate-200 p-4" data-cookie-consent-option="site-preferences">
                    <div class="flex items-start justify-between gap-4">
                        <div>
                            <h3 class="text-sm font-semibold text-slate-900">{{ t('core.cookies.site_preferences_title') }}</h3>
                            <p class="mt-1 text-xs leading-relaxed text-slate-500">
                                {{ t('core.cookies.site_preferences_description') }}
                            </p>
                        </div>
                        <label class="relative inline-flex cursor-pointer items-center">
                            <input type="checkbox" class="peer sr-only" data-cookie-consent-toggle="site-preferences">
                            <span class="h-6 w-11 rounded-full bg-slate-200 after:absolute after:left-1 after:top-1 after:h-4 after:w-4 after:rounded-full after:bg-white after:transition peer-checked:bg-slate-900 peer-checked:after:translate-x-5"></span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                <div class="flex flex-col gap-3 sm:flex-row sm:gap-2">
                    <button type="button" class="w-full rounded-md bg-slate-900 px-4 py-2 text-sm font-semibold text-white transition hover:bg-slate-700 sm:w-auto" data-cookie-consent-modal-accept>
                        {{ t('core.cookies.accept_all') }}
                    </button>
                    <button type="button" class="w-full rounded-md bg-slate-900 px-4 py-2 text-sm font-semibold text-white transition hover:bg-slate-700 sm:w-auto" data-cookie-consent-modal-decline>
                        {{ t('core.cookies.reject_all') }}
                    </button>
                </div>
                <button type="button" class="w-full rounded-md border border-slate-200 bg-slate-100 px-4 py-2 text-sm font-medium text-slate-700 transition hover:bg-slate-200 sm:w-auto" data-cookie-consent-modal-save>
                    {{ t('core.cookies.save_preferences') }}
                </button>
            </div>
        </div>
    </div>
</div>
