@if(config('cookie-consent.enabled'))

    @include('cookie-consent::dialogContents')

    <script>

        window.laravelCookieConsent = (function () {

            const COOKIE_NAME = '{{ $cookieConsentConfig['cookie_name'] }}';
            const COOKIE_ACCEPTED_VALUE = '1';
            const COOKIE_DECLINED_VALUE = '0';
            const COOKIE_CUSTOM_PREFIX = 'custom:';
            const COOKIE_DOMAIN = '{{ config('session.domain') ?? request()->getHost() }}';
            const COOKIE_LIFETIME = {{ $cookieConsentConfig['cookie_lifetime'] }};
            const DEFAULT_PREFERENCES = { sitePreferences: false };
            const MODAL_TRANSITION_DURATION = 200;

            const modal = document.querySelector('[data-cookie-consent-modal]');
            const modalOverlay = modal ? modal.querySelector('[data-cookie-consent-modal-overlay]') : null;
            const modalPanel = modal ? modal.querySelector('[data-cookie-consent-modal-panel]') : null;
            const modalCloseButton = modal ? modal.querySelector('[data-cookie-consent-modal-close]') : null;
            const modalSaveButton = modal ? modal.querySelector('[data-cookie-consent-modal-save]') : null;
            const modalAcceptButton = modal ? modal.querySelector('[data-cookie-consent-modal-accept]') : null;
            const modalDeclineButton = modal ? modal.querySelector('[data-cookie-consent-modal-decline]') : null;
            const modalSitePreferencesToggle = modal ? modal.querySelector('[data-cookie-consent-toggle="site-preferences"]') : null;

            const acceptButtons = document.getElementsByClassName('js-cookie-consent-accept');
            const declineButtons = document.getElementsByClassName('js-cookie-consent-decline');
            const preferenceButtons = document.getElementsByClassName('js-cookie-consent-preferences');

            let lastKnownPreferences = Object.assign({}, DEFAULT_PREFERENCES);

            function consentWithCookies(value, options = {}) {
                setCookie(COOKIE_NAME, value, COOKIE_LIFETIME);
                hideCookieDialog();

                // if (options.reload) {
                //     window.location.reload();
                // }
            }

            function rejectCookies() {
                lastKnownPreferences = Object.assign({}, DEFAULT_PREFERENCES);
                consentWithCookies(COOKIE_DECLINED_VALUE);
                closePreferencesModal();
            }

            function cookieDecisionMade(name) {
                return document.cookie.split('; ').some(function (cookie) {
                    return cookie.startsWith(name + '=');
                });
            }

            function hideCookieDialog() {
                const dialogs = document.getElementsByClassName('js-cookie-consent');

                for (let i = 0; i < dialogs.length; ++i) {
                    dialogs[i].style.display = 'none';
                }
            }

            function setCookie(name, value, expirationInDays) {
                const date = new Date();
                date.setTime(date.getTime() + (expirationInDays * 24 * 60 * 60 * 1000));
                document.cookie = name + '=' + value
                    + ';expires=' + date.toUTCString()
                    + ';domain=' + COOKIE_DOMAIN
                    + ';path=/{{ config('session.secure') ? ';secure' : null }}'
                    + '{{ config('session.same_site') ? ';samesite='.config('session.same_site') : null }}';
            }

            function getCookie(name) {
                const target = name + '=';
                const segments = document.cookie.split(';');

                for (let i = 0; i < segments.length; i++) {
                    const cookie = segments[i].trim();

                    if (cookie.indexOf(target) === 0) {
                        return cookie.substring(target.length);
                    }
                }

                return null;
            }

            function parsePreferences(value) {
                if (!value) {
                    return Object.assign({}, DEFAULT_PREFERENCES);
                }

                if (value === COOKIE_ACCEPTED_VALUE) {
                    return { sitePreferences: true };
                }

                if (value === COOKIE_DECLINED_VALUE) {
                    return Object.assign({}, DEFAULT_PREFERENCES);
                }

                if (value.indexOf(COOKIE_CUSTOM_PREFIX) === 0) {
                    const encoded = value.substring(COOKIE_CUSTOM_PREFIX.length);

                    try {
                        const decoded = atob(encoded);
                        const parsed = JSON.parse(decoded);

                        return {
                            sitePreferences: parsed.sitePreferences !== undefined
                                ? Boolean(parsed.sitePreferences)
                                : Boolean(parsed.analytics)
                        };
                    } catch (error) {
                        console.warn('Unable to parse cookie preferences', error);
                        return Object.assign({}, DEFAULT_PREFERENCES);
                    }
                }

                return Object.assign({}, DEFAULT_PREFERENCES);
            }

            function applyPreferencesToControls(preferences) {
                if (modalSitePreferencesToggle) {
                    modalSitePreferencesToggle.checked = Boolean(preferences.sitePreferences);
                }
            }

            function collectPreferencesFromControls() {
                return {
                    sitePreferences: modalSitePreferencesToggle ? modalSitePreferencesToggle.checked : false
                };
            }

            function openPreferencesModal() {
                if (!modal) {
                    return;
                }

                lastKnownPreferences = parsePreferences(getCookie(COOKIE_NAME));
                applyPreferencesToControls(lastKnownPreferences);

                modal.classList.remove('hidden');

                requestAnimationFrame(function () {
                    if (modalOverlay) {
                        modalOverlay.classList.remove('opacity-0');
                        modalOverlay.classList.add('opacity-100');
                    }

                    if (modalPanel) {
                        modalPanel.classList.remove('translate-y-6');
                        modalPanel.classList.remove('scale-95');
                    }

                    modal.classList.remove('pointer-events-none');
                });

                document.addEventListener('keydown', handleEscape, true);
            }

            function closePreferencesModal(options = {}) {
                if (!modal || modal.classList.contains('hidden')) {
                    return;
                }

                const immediate = Boolean(options.immediate);

                if (modalOverlay) {
                    modalOverlay.classList.add('opacity-0');
                    modalOverlay.classList.remove('opacity-100');
                }

                if (modalPanel) {
                    modalPanel.classList.add('translate-y-6');
                    modalPanel.classList.add('scale-95');
                }

                modal.classList.add('pointer-events-none');

                const finalize = function () {
                    modal.classList.add('hidden');
                };

                if (immediate) {
                    finalize();
                } else {
                    setTimeout(finalize, MODAL_TRANSITION_DURATION);
                }

                document.removeEventListener('keydown', handleEscape, true);
            }

            function handleEscape(event) {
                if (event.key === 'Escape') {
                    closePreferencesModal();
                }
            }

            function saveCustomPreferences() {
                const preferences = collectPreferencesFromControls();
                lastKnownPreferences = preferences;
                const encoded = btoa(JSON.stringify(preferences));
                const shouldReload = Boolean(preferences.sitePreferences);

                if (shouldReload) {
                    closePreferencesModal({ immediate: true });
                    consentWithCookies(COOKIE_CUSTOM_PREFIX + encoded, { reload: true });
                    return;
                }

                consentWithCookies(COOKIE_CUSTOM_PREFIX + encoded);
                closePreferencesModal();
            }

            if (cookieDecisionMade(COOKIE_NAME)) {
                hideCookieDialog();
            }

            for (let i = 0; i < acceptButtons.length; ++i) {
                acceptButtons[i].addEventListener('click', function () {
                    lastKnownPreferences = { sitePreferences: true };
                    closePreferencesModal({ immediate: true });
                    consentWithCookies(COOKIE_ACCEPTED_VALUE, { reload: true });
                });
            }

            for (let i = 0; i < declineButtons.length; ++i) {
                declineButtons[i].addEventListener('click', rejectCookies);
            }

            for (let i = 0; i < preferenceButtons.length; ++i) {
                preferenceButtons[i].addEventListener('click', function () {
                    window.dispatchEvent(new CustomEvent('cookie-consent:preferences'));
                });
            }

            if (modalOverlay) {
                modalOverlay.addEventListener('click', function () {
                    closePreferencesModal();
                });
            }

            if (modalCloseButton) {
                modalCloseButton.addEventListener('click', function () {
                    closePreferencesModal();
                });
            }

            if (modalSaveButton) {
                modalSaveButton.addEventListener('click', saveCustomPreferences);
            }

            if (modalAcceptButton) {
                modalAcceptButton.addEventListener('click', function () {
                    lastKnownPreferences = { sitePreferences: true };
                    closePreferencesModal();
                    consentWithCookies(COOKIE_ACCEPTED_VALUE, { reload: true });
                });
            }

            if (modalDeclineButton) {
                modalDeclineButton.addEventListener('click', rejectCookies);
            }

            window.addEventListener('cookie-consent:preferences', openPreferencesModal);

            return {
                consentWithCookies: function () {
                    lastKnownPreferences = { sitePreferences: true };
                    closePreferencesModal({ immediate: true });
                    consentWithCookies(COOKIE_ACCEPTED_VALUE, { reload: true });
                },
                rejectCookies: rejectCookies,
                hideCookieDialog: hideCookieDialog,
                openPreferences: openPreferencesModal
            };
        })();
    </script>

@endif
