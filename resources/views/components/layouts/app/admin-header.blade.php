<div class="sticky top-0 z-50 shadow-sm shadow-gray-300/50 bg-white">
    <nav class="relative text-black">
        <div class="navbar max-w-6xl w-full justify-between items-center mx-auto">
            <div class="navbar-start flex items-center gap-8">
                <a href="/" class="flex justify-center items-center text-black font-bold">
                    <span class="text-xl">
                        {{ config('app.name') }}
                    </span>
                </a>
            </div>
            <div class="navbar-end ">
                <div x-data="{ open: false }" class="relative">
                    <div
                        @click="open = !open"
                        class="btn btn-ghost btn-lang text-black cursor-pointer"
                    >
                        <span class="font-medium uppercase">
                            {{ strtoupper(config('app.locale')) }}
                        </span>
                        <svg class="ms-1" height="12.5px" width="12.5px" fill="currentColor" viewBox="0 0 483.049 483.049">
                            <polygon points="0,121.155 241.524,361.894 241.524,121.155"></polygon>
                            <polygon points="241.524,121.155 241.524,361.894 483.049,121.155"></polygon>
                        </svg>
                    </div>

                    <div
                        x-show="open"
                        x-cloak
                        @click.away="open = false"
                        @keydown.escape.window="open = false"
                        x-transition
                        class="absolute end-0 rtl:start-auto mt-2 z-50"
                    >
                        <x-layouts.app.language-list updateSessionLanguage="true" />
                    </div>
                </div>
            </div>
        </div>
    </nav>
</div>


