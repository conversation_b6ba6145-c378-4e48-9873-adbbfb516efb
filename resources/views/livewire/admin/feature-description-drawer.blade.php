<div class="space-y-3">
    <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold">{{ ucfirst($featureKey) }} {{ __('Description') }}</h2>
        @if(!$isEditing)
            <button type="button" wire:click="toggleEdit" class="text-primary-600 hover:text-primary-700">
                <x-heroicon-o-pencil-square class="h-5 w-5" />
            </button>
        @else
            <div class="inline-flex items-center rounded-md bg-gray-100 p-1">
                <button type="button"
                        wire:click="$set('showPreview', false)"
                        class="px-3 py-1.5 text-sm font-semibold rounded-md focus:outline-none {{ $showPreview ? 'text-gray-600' : 'bg-white shadow text-gray-900' }}">
                    {{ t('core.common.code') }}
                </button>
                <button type="button"
                        wire:click="$set('showPreview', true)"
                        class="px-3 py-1.5 text-sm font-semibold rounded-md focus:outline-none {{ $showPreview ? 'bg-white shadow text-gray-900' : 'text-gray-600' }}">
                    {{ t('core.common.preview') }}
                </button>
            </div>
        @endif
    </div>

    @if(!$isEditing)
        <div class="prose max-w-none">
            {!! $this->html !!}
        </div>
    @else
        <div class="space-y-3">
            @if(!$showPreview)
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <label class="inline-flex items-center gap-2 rounded-md bg-gray-100 p-1">
                            <span class="sr-only">{{ t('core.common.insert_image') }}</span>
                            <input type="file" wire:model="image" accept="image/*" class="hidden" id="fd-image-input">
                            <button type="button" onclick="document.getElementById('fd-image-input').click()" class="px-3 py-1.5 text-sm font-semibold rounded-md focus:outline-none cursor-pointer bg-white shadow text-gray-900">
                                <x-heroicon-o-photo class="h-5 w-5 inline" />
                                {{ t('core.common.insert_image') }}
                            </button>
                        </label>
                        <x-filament::loading-indicator wire:loading wire:target="image" class="w-4 h-4" />
                    </div>
                </div>
                {{ $this->form }}
            @else
                <div class="prose max-w-none border rounded-md p-3 bg-white">
                    {!! $this->previewHtml !!}
                </div>
            @endif

            <div class="flex items-center justify-end gap-2 pt-2">
                <x-filament::button wire:click="cancel" color="gray" icon="heroicon-o-x-mark">
                    {{ t('core.cancel') }}
                </x-filament::button>
                <x-filament::button wire:click="save" icon="heroicon-o-check">
                    {{ t('core.save') }}
                </x-filament::button>
            </div>
        </div>
    @endif
</div>
