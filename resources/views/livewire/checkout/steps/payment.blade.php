<div class="mt-6 flex items-start justify-center space-x-2 gap-6">
    <div class="max-w-6xl px-4">
        @if ($accounting['total']<0)
             <p class="max-w-md text-green-500 text-center pb-2 font-bold">
                {{ t('ui_checkout_refund_confirmation_msg', ['totalToRefund' => money(abs($accounting['total']), 'eur')]) }}
            </p>
        @endif

        <div class="min-w-[300px] lg:min-w-[400px] rounded-2xl overflow-hidden">
            <script src="https://js.stripe.com/v3/"></script>
            <div x-data="stripePaymentComponent()" x-init="init()" :key="'stripe-' + step">
                <div id="loading" x-show="loading" class="mb-2 text-gray-600 w-full">{{ t('ui_checkout_loading') }}</div>
                <div id="checkout-stripe" class="w-full"></div>
                <div x-show="errorMessage" x-text="errorMessage" class="text-red-600 mt-2"></div>
            </div>
        </div>
    </div>

    <div class="rounded-lg bg-gray-100 overflow-hidden p-6 space-y-4 text-black">
        <h2 class="text-lg mb-3">{{ t('ui_checkout_myorder') }}</h2>

        <h2 class="text-lg">{{ t('ui_checkout_sub') }}</h2>
        <h2 class="text-lg mb-3">{{ $plan->product->name }}</h2>

        <h2 class="text-lg">{{ $plan->product->account_type }}</h2>

        <h2 class="text-lg mb-3 font-bold">
            @if ($accounting['total'] >= 0)
                <span>{{ t('ui_checkout_amount_due') }}</span>
            @else
                <span>{{ t('ui_checkout_refund_amount') }}</span>
            @endif
            : @money(abs($accounting['total']), 'eur')

        </h2>

        <h2 class="text-lg font-bold">{{ t('ui_checkout_mybill') }}</h2>
        <h2 class="text-sm mb-3"> {{ $name }} </h2>
        <h2 class="text-sm mb-3"> {{ $email }} </h2>
        <h2 class="text-sm mb-3"> {{ $country }} </h2>

    </div>
</div>

<script>
    const total = (+"{{ $accounting['total'] }}")/100;
    const productPrice = +"{{ $plan->price_directly }}";
    const discount = productPrice - total;
    const googleAnalyticsEventData = {
        affiliation: "{{ config('app.name') }}",
        currency: "EUR",
        client_id: "{{ auth()->id() }}",
        value: total,
        tax: 0,
        shipping: 0,
        items: [
        {
            "item_id": "{{ $plan->product->id }}",
            "item_name":  "{{ $plan->product->name }}",
            "quantity": 1,
            "price": productPrice,
            "discount": discount,
            "index": "{{ $plan->id }}"
        }
        ]
    };
    function stripePaymentComponent() {
        return {
            stripe: null,
            loading: true,
            errorMessage: '',
            embeddedCheckoutInitialized: false,
            clientSecret: @json($clientSecret),

            init() {
                this.initializeStripe();
                document.addEventListener('reinitialize-stripe', () => {
                    this.initializeStripe();
                });
            },

            initializeStripe() {
                if (this.embeddedCheckoutInitialized) return;
                this.embeddedCheckoutInitialized = true;

                try {
                    this.stripe = Stripe("{{ $stripePublishableKey }}");

                    this.stripe.initEmbeddedCheckout({
                        fetchClientSecret: async () => {
                            return this.clientSecret;
                        }
                    }).then((checkout) => {
                        checkout.mount('#checkout-stripe');
                        this.loading = false;
                        console.log('googleAnalyticsEventData in x-data', googleAnalyticsEventData);
                        gtag('event', 'begin_checkout', googleAnalyticsEventData);
                    }).catch((error) => {
                        this.errorMessage = error.message;
                        this.loading = false;
                    });
                } catch (error) {
                    this.errorMessage = error.message;
                    this.loading = false;
                }
            }
        }
    }
</script>
