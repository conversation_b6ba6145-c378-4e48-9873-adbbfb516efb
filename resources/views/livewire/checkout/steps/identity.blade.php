<?php
use App\Constants\CheckoutStepEnum;
?>

@guest
    <div class="mt-6 flex items-center justify-center gap-x-4">
        <div class="w-1/2">
            @include('livewire.checkout.partials.must-login')
        </div>
    </div>
@else
    <div class="mt-6 w-full max-w-4xl mx-auto p-6 rounded-lg">
        <div class="flex w-full gap-4 items-start">
            <form wire:submit.prevent="saveUserAddress" class="w-[70%] rounded-lg overflow-hidden pb-6 space-y-6 pe-4">

                <div class="flex space-x-6">
                    <label class="flex items-center">
                        <input type="radio" name="civility" checked value="monsieur" class="w-4 h-4 text-primary_ui-600 border-primary_ui-300 focus:ring-primary_ui-500">
                        <span class="ms-2 text-gray-700">Monsieur</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="civility" value="madame" class="w-4 h-4 text-primary_ui-600 border-primary_ui-300 focus:ring-primary_ui-500">
                        <span class="ms-2 text-gray-700">Madame</span>
                    </label>
                </div>


                <input type="text" value="{{ $user['last_name'] }}" wire:model.defer="name" placeholder="Name"
                    class="w-full px-4 py-3 border border-primary_ui-50 rounded-lg focus:ring-2 focus:ring-primary_ui-500 focus:border-primary_ui-500 outline-none transition-colors">

                <div class="relative">
                    <select wire:model.defer="countryId"
                        class="w-full px-4 py-3 border border-primary_ui-50 rounded-lg focus:ring-2 focus:ring-primary_ui-500 focus:border-primary_ui-500 outline-none transition-colors appearance-none bg-white">
                        @foreach ($this->countryListFromDb as $c)
                            <option value="{{ $c['id'] }}">{{ $c['name'] }}</option>
                        @endforeach
                    </select>
                </div>

                <input type="text" placeholder="Adress" wire:model.defer="address"
                    class="w-full px-4 py-3 border border-primary_ui-50 rounded-lg focus:ring-2 focus:ring-primary_ui-500 focus:border-primary_ui-500 outline-none transition-colors">

                <input type="text" placeholder="City" wire:model.defer="city"
                    class="w-full px-4 py-3 border border-primary_ui-50 rounded-lg focus:ring-2 focus:ring-primary_ui-500 focus:border-primary_ui-500 outline-none transition-colors">

                <div class="flex">
                    <input
                        type="tel"
                        wire:model.defer="phoneNumber"
                        placeholder="e.g. 123 456 789"
                        class="w-full px-4 py-3 border border-primary_ui-50 rounded-lg focus:ring-2 focus:ring-primary_ui-500 focus:border-primary_ui-500 outline-none transition-colors"
                    >
                </div>

                <input type="email" wire:model.defer="email" value="{{ $user['email'] }}" placeholder="Email"
                    class="w-full px-4 py-3 border border-primary_ui-50 rounded-lg focus:ring-2 focus:ring-primary_ui-500 focus:border-primary_ui-500 outline-none transition-colors">

                <div class="flex justify-between pt-4">
                    <a
                        href="{{ route('checkout.cart_wizard', ['stepSlug' => CheckoutStepEnum::CART->value]) }}"
                        class="me-auto flex flex-row items-center justify-center disabled:opacity-40"
                    >
                        < {{ t('ui_checkout_back') }}
                    </a>
                    <button type="submit"
                        class="w-[12rem] bg-orange-500 text-center hover:bg-orange-600 text-white font-bold py-3 px-6 rounded transition">
                        Save
                    </button>
                </div>
            </form>

            <div class="w-[30%] rounded-lg bg-gray-100 overflow-hidden p-6 space-y-4 text-black">
                <h2 class="text-lg mb-3">{{ t('ui_checkout_myorder') }}</h2>

                <h2 class="text-lg">{{ t('ui_checkout_sub') }}</h2>
                <h2 class="text-lg mb-3">{{ $plan->product->name }}</h2>

                <h2 class="text-lg">{{ $plan->product->account_type }}</h2>
                 <h2 class="text-lg mb-3 font-bold">
                    @if ($accounting['total'] >= 0)
                        <span>{{ t('ui_checkout_amount_due') }}</span>
                    @else
                        <span>{{ t('ui_checkout_refund_amount') }}</span>
                    @endif
                    : @money(abs($accounting['total']), 'eur')

                </h2>
            </div>
        </div>
    </div>
@endguest
