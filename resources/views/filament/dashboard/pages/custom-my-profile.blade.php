<x-filament-panels::page>
    <div class="rounded p-5 space-y-8 border-[1px] border-primary_ui_high-900">
        @foreach ($this->getMyProfileComponents()->slice(0, -1) as $component)
            @livewire($component, key($component))
        @endforeach
    </div>

    @if ($last = $this->getMyProfileComponents()->last())
        <div class="mb-8 space-y-6">
            @livewire($last, key($last))

            @php
                $hasTwoFactorEnabled = auth()->user()?->hasTwoFactorEnabled();
            @endphp

            <div class="rounded border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-900 p-6 space-y-3">
                <h3 class="text-lg font-semibold text-primary_ui_high-900">
                    {{ __('Add an Extra Layer of Security') }}
                </h3>

                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ __('Enable two-factor authentication to keep your account even safer. After turning it on, you will confirm your sign-ins with both your password and a secure verification code.') }}
                </p>

                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ __('We recommend saving your recovery codes in a secure place in case you lose access to your device.') }}
                </p>

                <div class="flex items-center justify-between">
                    <div class="space-y-1">
                        <span class="text-sm font-medium text-primary_ui_high-900">
                            {{ __('Two-Factor Authentication') }}
                        </span>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $hasTwoFactorEnabled
                                ? __('Switch off to begin the two-factor disable process.')
                                : __('Switch on to start the two-factor setup process.') }}
                        </p>
                    </div>

                    <button
                        type="button"
                        role="switch"
                        aria-checked="{{ $hasTwoFactorEnabled ? 'true' : 'false' }}"
                        class="relative inline-flex h-8 w-16 items-center rounded-full border-2 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary_ui_high-500 focus:ring-offset-2 {{ $hasTwoFactorEnabled ? 'bg-[#084A8B] border-[#084A8B] hover:bg-[#0a5cb0] hover:border-[#0a5cb0]' : 'bg-gray-300 border-gray-300 hover:bg-primary_ui_high-400 hover:border-primary_ui_high-400' }}"
                        wire:click="{{ $hasTwoFactorEnabled ? 'disableTwoFactorFromProfile' : 'enableTwoFactorFromProfile' }}"
                        wire:loading.attr="disabled"
                    >
                        <span class="sr-only">
                            {{ $hasTwoFactorEnabled
                                ? __('Disable Two-Factor Authentication')
                                : __('Enable Two-Factor Authentication') }}
                        </span>
                        <span
                            aria-hidden="true"
                            class="inline-block h-6 w-6 rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out {{ $hasTwoFactorEnabled ? 'translate-x-8' : 'translate-x-2' }}"
                        ></span>
                    </button>
                </div>
            </div>
        </div>
    @endif
</x-filament-panels::page>
