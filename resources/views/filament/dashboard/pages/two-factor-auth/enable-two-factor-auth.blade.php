<x-filament-panels::page>
    <div class="rounded border-[1px] border-primary_ui_high-900 bg-white p-6 space-y-6 dark:bg-gray-900">
        <div class="space-y-2 text-center">
            <h1 class="text-2xl font-semibold text-primary_ui_high-900">
                {{ __('Enable Two-Factor Authentication') }}
            </h1>
            <p class="text-sm text-gray-600 dark:text-gray-300">
                {{ __('Keep your account secure by adding an extra verification step during sign in.') }}
            </p>
        </div>

        <div class="flex flex-col items-center gap-5">
            <div class="rounded border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
                {!! $qrCode !!}
            </div>

            <div class="text-center text-sm leading-relaxed text-gray-700 dark:text-gray-200">
                {{ __('Scan the QR code with your authenticator app to link your account.') }}
                <br />
                {{ __('If you can\'t scan the QR code, enter the setup key shown below.') }}
            </div>

            <div class="w-full rounded border border-dashed border-primary_ui_high-900 bg-gray-100 px-4 py-3 text-center text-base font-bold text-primary_ui_high-900 dark:border-primary_ui_high-700 dark:bg-gray-800">
                {{ $stringCode }}
            </div>
        </div>

        <p class="text-center text-sm text-gray-600 dark:text-gray-300">
            {{ __('You will confirm the setup with a verification code on the next screen.') }}
        </p>

        <div class="flex justify-center">
            <x-filament::button wire:click="confirmEnableTwoFactorAuth" size="md" class="!min-w-48">
                {{ __('Confirm Two-Factor Authentication Code') }}
            </x-filament::button>
        </div>
    </div>
</x-filament-panels::page>
