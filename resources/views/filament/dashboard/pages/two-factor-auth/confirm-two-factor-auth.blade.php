<x-filament-panels::page>
    <div class="rounded border-[1px] border-primary_ui_high-900 bg-white p-6 space-y-6 dark:bg-gray-900">
        <div class="space-y-2 text-center">
            <h1 class="text-2xl font-semibold text-primary_ui_high-900">
                {{ __('Confirm Your Two-Factor Code') }}
            </h1>
            <p class="text-sm text-gray-600 dark:text-gray-300">
                {{ __('Enter the verification code from your authenticator app to finish enabling two-factor authentication.') }}
            </p>
        </div>

        <form wire:submit.prevent="confirmTwoFactorAuth" class="space-y-6">
            <div class="mx-auto w-full max-w-sm">
                <label for="code" class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ __('Authentication code') }}
                </label>

                <x-filament::input.wrapper>
                    <x-filament::input
                        id="code"
                        type="text"
                        wire:model.defer="code"
                        class="w-full"
                        autofocus
                        autocomplete="one-time-code"
                        placeholder="{{ t('core.enter_two_factor') }}"
                    />
                </x-filament::input.wrapper>

                @error('code')
                    <span class="mt-2 block text-xs text-red-500" role="alert">
                        {{ $message }}
                    </span>
                @enderror
            </div>

            <div class="flex justify-center gap-4">
                <x-filament::button type="button" tag="button" wire:click="cancel">
                    {{ __('Cancel') }}
                </x-filament::button>
                <x-filament::button type="submit" size="md" class="!min-w-48">
                    {{ __('Confirm Two-Factor Authentication Code') }}
                </x-filament::button>
            </div>
        </form>
    </div>

</x-filament-panels::page>
