@php
    $sections = $getState() ?? [];
    $html = $sections['html'] ?? null;
    $error = $sections['error'] ?? null;

    $encodedHtml = base64_encode($html ?? '');
    $encodedError = base64_encode($error ?? '');
@endphp

<div
    x-data="emailTemplatePreview()"
    x-init="init()"
    
>
    <div
        x-ref="payload"
        data-html="{{ $encodedHtml }}"
        data-error="{{ $encodedError }}"
        hidden
    ></div>

    <template x-if="modalMounted">
        <div
            x-show="showModal"
            x-cloak
            class="fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4"
            @keydown.window.escape.prevent.stop="closeModal()"
        >
            <div class="relative flex h-full w-full max-h-full max-w-7xl flex-col gap-4 overflow-hidden rounded-2xl bg-white p-6 shadow-xl dark:bg-gray-900">
                <div class="flex items-center justify-between gap-4">
                    <div class="flex flex-col">
                        <span class="text-sm font-semibold text-gray-800 dark:text-gray-100">Template Preview</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400" x-text="deviceLabel"></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-filament::icon-button icon="heroicon-o-arrows-right-left" label="Toggle controls" @click="showControls = ! showControls" class="hidden sm:flex" />
                        <x-filament::icon-button icon="heroicon-o-x-mark" label="Close" @click="closeModal()" />
                    </div>
                </div>

                <div class="flex flex-1 flex-col gap-4 overflow-hidden">
                    <div class="flex flex-col gap-4" x-show="showControls" x-transition>
                        <div class="flex flex-wrap items-center gap-2">
                            <template x-for="option in devices" :key="option.key">
                                <button
                                    type="button"
                                    class="rounded-full border border-gray-200 px-3 py-1 text-xs font-medium text-gray-600 transition hover:border-primary-500 hover:text-primary-600 dark:border-gray-700 dark:text-gray-300 dark:hover:border-primary-400"
                                    :class="device === option.key ? 'bg-primary-500 text-white shadow-sm hover:border-primary-500 hover:text-white dark:hover:border-primary-500' : ''"
                                    @click="setDevice(option.key)"
                                    x-text="option.label"
                                ></button>
                            </template>
                        </div>
                        <div class="flex items-center gap-4 text-xs text-gray-600 dark:text-gray-300">
                            <label class="flex items-center gap-2">
                                <span>Custom width</span>
                                <input
                                    type="range"
                                    min="320"
                                    max="1600"
                                    step="10"
                                    x-model.number="customWidth"
                                    @input="setDevice('custom')"
                                    class="h-1.5 w-48 cursor-pointer rounded-lg bg-gray-200 accent-primary-500 dark:bg-gray-700"
                                />
                            </label>
                            <span x-text="customWidth + 'px'"></span>
                        </div>
                    </div>

                    <div class="relative flex-1 overflow-auto rounded-2xl border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-950">
                        <template x-if="isLoading">
                            <div class="absolute inset-0 z-10 flex items-center justify-center rounded-2xl bg-white/80 dark:bg-gray-900/80">
                                <x-filament::loading-indicator class="h-8 w-8" />
                            </div>
                        </template>
                        <div class="mx-auto flex h-full w-full flex-col" :style="frameStyle">
                            <template x-if="error">
                                <div class="flex h-full w-full flex-col items-center justify-center gap-3 p-6 text-center text-sm text-red-600 dark:text-red-400">
                                    <x-heroicon-o-exclamation-triangle class="h-10 w-10" />
                                    <p x-text="error"></p>
                                </div>
                            </template>

                            <template x-if="! error && html">
                                <iframe
                                    x-ref="iframe"
                                    class="h-full w-full rounded-xl bg-white shadow-sm"
                                    :style="frameInnerStyle"
                                    :srcdoc="html"
                                    sandbox="allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-top-navigation-by-user-activation"
                                ></iframe>
                            </template>

                            <template x-if="! error && ! html">
                                <div class="flex h-full w-full flex-col items-center justify-center gap-3 p-6 text-center text-sm text-gray-500 dark:text-gray-400">
                                    <x-heroicon-o-eye class="h-10 w-10 text-gray-300 dark:text-gray-600" />
                                    <p>Make your edits and click refresh to see the updated preview.</p>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <div class="mt-3 flex justify-end gap-3 sm:absolute sm:right-6 sm:top-4 sm:mt-0 sm:z-10">
        <x-filament::button
            size="sm"
            x-on:click="openModal()"
            x-bind:disabled="isLoading"
            x-bind:class="{ 'pointer-events-none opacity-70': isLoading }"
        >
            <span x-show="! isLoading" x-cloak class="flex items-center gap-2">
                <x-filament::icon icon="heroicon-o-arrows-pointing-out" class="h-4 w-4" />
                <span>Open Preview</span>
            </span>
            <span x-show="isLoading" x-cloak class="flex items-center gap-2">
                <x-filament::loading-indicator class="h-4 w-4" />
                <span>Loading...</span>
            </span>
        </x-filament::button>
    </div>

    <template x-if="error">
        <div class="mt-3 rounded-lg border border-red-200 bg-red-50 p-3 text-xs text-red-600 dark:border-red-700 dark:bg-red-900/20 dark:text-red-400">
            <strong class="font-semibold">Preview error:</strong>
            <span x-text="error"></span>
        </div>
    </template>
</div>

@push('scripts')
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('emailTemplatePreview', () => ({
                devices: [
                    { key: 'mobile', label: 'Mobile', width: 390 },
                    { key: 'tablet', label: 'Tablet', width: 768 },
                    { key: 'desktop', label: 'Desktop', width: 1280 },
                    { key: 'full', label: 'Full', width: null },
                    { key: 'custom', label: 'Custom', width: null },
                ],
                device: 'desktop',
                customWidth: 900,
                showModal: false,
                showControls: true,
                modalMounted: false,
                error: null,
                html: null,
                observer: null,
                isLoading: false,
                loadingTimeoutId: null,
                iframeLoadHandler: null,
                init() {
                    this.iframeLoadHandler = () => this.stopLoading();
                    this.refreshPayload();
                    this.modalMounted = true;
                },
                openModal() {
                    this.startLoading();

                    const refresh = this.$wire?.$refresh;

                    const proceed = () => {
                        this.showModal = true;
                        this.refreshPayload();
                        this.$nextTick(() => this.finishLoading());
                    };

                    if (typeof refresh === 'function') {
                        const result = refresh.call(this.$wire);

                        if (result && typeof result.then === 'function') {
                            result.finally(proceed);

                            return;
                        }
                    }

                    proceed();
                },
                closeModal() {
                    this.showModal = false;
                    this.stopLoading();
                },
                startLoading() {
                    this.resetLoadingWatcher();
                    this.isLoading = true;
                },
                finishLoading() {
                    if (! this.isLoading) {
                        return;
                    }

                    if (! this.showModal) {
                        this.stopLoading();
                        return;
                    }

                    if (this.error) {
                        this.stopLoading();
                        return;
                    }

                    const iframe = this.$refs.iframe;

                    this.resetLoadingWatcher();

                    if (! this.html || ! iframe) {
                        this.loadingTimeoutId = window.setTimeout(() => this.stopLoading(), 600);

                        return;
                    }

                    iframe.addEventListener('load', this.iframeLoadHandler, { once: true });
                    this.loadingTimeoutId = window.setTimeout(() => this.stopLoading(), 1500);
                },
                stopLoading() {
                    if (! this.isLoading) {
                        this.resetLoadingWatcher();

                        return;
                    }

                    this.isLoading = false;
                    this.resetLoadingWatcher();
                },
                resetLoadingWatcher() {
                    if (this.loadingTimeoutId !== null) {
                        clearTimeout(this.loadingTimeoutId);
                        this.loadingTimeoutId = null;
                    }

                    const iframe = this.$refs.iframe;

                    if (iframe && this.iframeLoadHandler) {
                        iframe.removeEventListener('load', this.iframeLoadHandler);
                    }
                },
                setDevice(key) {
                    this.device = key;
                    if (key !== 'custom' && key !== 'full') {
                        const match = this.devices.find(device => device.key === key);
                        if (match && match.width) {
                            this.customWidth = match.width;
                        }
                    }
                },
                refreshPayload() {
                    const payload = this.$refs.payload;
                    if (! payload) {
                        return;
                    }

                    this.html = this.decode(payload.dataset.html ?? '');
                    this.error = this.decode(payload.dataset.error ?? '') || null;

                    this.observePayloadChanges();

                    if (this.isLoading && this.showModal) {
                        this.$nextTick(() => this.finishLoading());
                    }

                    if (this.isLoading && this.error) {
                        this.stopLoading();
                    }
                },
                observePayloadChanges() {
                    const payload = this.$refs.payload;

                    if (this.observer) {
                        this.observer.disconnect();
                        this.observer = null;
                    }

                    if (! payload || typeof MutationObserver === 'undefined') {
                        return;
                    }

                    this.observer = new MutationObserver(() => this.refreshPayload());
                    this.observer.observe(payload, { attributes: true, attributeFilter: ['data-html', 'data-error'] });
                },
                decode(value) {
                    if (! value) {
                        return '';
                    }

                    try {
                        const decoded = window.atob(value);
                        const percentEncoded = Array.from(decoded)
                            .map((char) => '%' + char.charCodeAt(0).toString(16).padStart(2, '0'))
                            .join('');

                        return decodeURIComponent(percentEncoded);
                    } catch (error) {
                        console.warn('Failed to decode preview payload.', error);
                        return '';
                    }
                },
                get frameStyle() {
                    if (this.device === 'full') {
                        return 'width: 100%; max-width: 100%; height: 100%;';
                    }

                    const width = this.device === 'custom'
                        ? this.customWidth
                        : (this.devices.find(device => device.key === this.device)?.width ?? this.customWidth);

                    return `width: ${width}px; max-width: 100%; height: 100%;`;
                },
                get frameInnerStyle() {
                    if (this.device === 'full') {
                        return '';
                    }

                    const width = this.device === 'custom'
                        ? this.customWidth
                        : (this.devices.find(device => device.key === this.device)?.width ?? this.customWidth);

                    return `width: ${width}px; max-width: 100%;`;
                },
                get deviceLabel() {
                    const match = this.devices.find(device => device.key === this.device);
                    if (! match) {
                        return '';
                    }

                    if (match.key === 'full') {
                        return 'Full width';
                    }

                    const width = match.width ?? this.customWidth;

                    return `${match.label} (${width}px)`;
                },
            }));
        });
    </script>
@endpush
