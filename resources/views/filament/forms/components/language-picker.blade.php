@php
    use Illuminate\Support\Collection;

    $fieldLabel = $label ?? 'Language';
    if ($fieldLabel instanceof \Closure) {
        $fieldLabel = $fieldLabel();
    }
    if ($fieldLabel instanceof \Illuminate\Support\HtmlString || $fieldLabel instanceof \Stringable) {
        $fieldLabel = (string) $fieldLabel;
    }

    $fieldPlaceholder = $placeholder ?? 'Select language';
    if ($fieldPlaceholder instanceof \Closure) {
        $fieldPlaceholder = $fieldPlaceholder();
    }
    if ($fieldPlaceholder instanceof \Illuminate\Support\HtmlString || $fieldPlaceholder instanceof \Stringable) {
        $fieldPlaceholder = (string) $fieldPlaceholder;
    }

    $isRequired = (bool) ($required ?? false);

    $formattedOptions = Collection::make($options ?? [])
        ->map(fn ($option) => [
            'id' => $option['id'] ?? null,
            'name' => $option['languageFull'] ?? null,
            'native' => $option['languageFullNative'] ?? ($option['languageFull'] ?? null),
            'flag' => $option['flagCode'] ?? null,
            'iso' => $option['languageISO2'] ?? null,
        ])
        ->filter(fn ($option) => $option['id'] !== null && $option['name'] !== null)
        ->values()
        ->all();
@endphp

<div
    class="flex flex-col gap-2"
    x-data="(() => {
        return {
            open: false,
            search: '',
            options: {{ Js::from($formattedOptions) }},
            state: @entangle('data.language_id').live,
            placeholder: @js($fieldPlaceholder),
            get filteredOptions() {
                if (this.search.trim() === '') {
                    return this.options;
                }

                const term = this.search.toLowerCase();

                return this.options.filter(option => {
                    return [option.name, option.native]
                        .filter(value => typeof value === 'string')
                        .some(value => value.toLowerCase().includes(term));
                });
            },
            get selectedOption() {
                return this.options.find(option => String(option.id) === String(this.state)) ?? null;
            },
            get displayIso() {
                const value = this.selectedOption?.iso ?? this.selectedOption?.name ?? '--';

                return value.toString().slice(0, 3).toUpperCase();
            },
            get displayLabel() {
                return this.selectedOption?.native ?? this.selectedOption?.name ?? this.placeholder;
            },
            select(option) {
                this.state = option.id;
                this.open = false;
                this.search = '';
            },
        };
    })()"
    x-id="['language-picker']"
>
    <label :for="$id('language-picker-trigger')" class="block text-sm font-medium text-gray-700 dark:text-gray-200">
        {{ $fieldLabel }}
        @if ($isRequired)
            <span class="text-red-500">*</span>
        @endif
    </label>

    <div class="relative" @keydown.escape.window="open = false">
        <button
            type="button"
            :id="$id('language-picker-trigger')"
            class="btn btn-ghost btn-lang flex items-center gap-2 border border-gray-200 px-3 py-2 dark:border-gray-700"
            @click="open = ! open"
            :aria-expanded="open.toString()"
            aria-haspopup="listbox"
        >
            <template x-if="selectedOption?.flag">
                <span :class="'flag-icon flag-icon-' + selectedOption.flag.toLowerCase()"></span>
            </template>
            <span class="font-medium uppercase" x-text="displayIso"></span>
            <svg class="ms-1 h-3 w-3" fill="currentColor" viewBox="0 0 483.049 483.049">
                <polygon points="0,121.155 241.524,361.894 241.524,121.155"></polygon>
                <polygon points="241.524,121.155 241.524,361.894 483.049,121.155"></polygon>
            </svg>
        </button>

        <div
            x-show="open"
            x-transition
            @click.away="open = false"
            class="absolute z-50 mt-2 w-[min(36rem,90vw)] rounded-xl border border-gray-200 bg-white p-3 shadow-2xl dark:border-gray-700 dark:bg-gray-900"
            role="listbox"
        >
            <div class="mb-3">
                <input
                    type="text"
                    x-model.trim="search"
                    placeholder="{{ __('Search languages…') }}"
                    class="w-full rounded-full border border-gray-200 px-4 py-2 text-sm shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white"
                />
            </div>

            <ul class="grid max-h-80 grid-cols-2 gap-2 overflow-y-auto pr-1 md:grid-cols-3 lg:grid-cols-4">
                <template x-for="option in filteredOptions" :key="option.id">
                    <li>
                        <button
                            type="button"
                            @click="select(option)"
                            class="language-option w-full rounded-xl px-3 py-2 text-left transition hover:bg-primary-100/70 dark:hover:bg-primary-500/20"
                            :class="{ 'ring-2 ring-primary-500 dark:ring-primary-400': String(option.id) === String(state) }"
                        >
                            <div class="language-option">
                                <template x-if="option.flag">
                                    <span :class="'flag-icon flag-icon-' + option.flag.toLowerCase()" class="language-flag"></span>
                                </template>
                                <span class="language-name" x-text="option.native ?? option.name"></span>
                                <span class="language-iso" x-text="(option.iso ?? '').toUpperCase()"></span>
                            </div>
                        </button>
                    </li>
                </template>

                <template x-if="filteredOptions.length === 0">
                    <li class="col-span-full py-6 text-center text-sm text-gray-500 dark:text-gray-400">
                        {{ __('No languages found.') }}
                    </li>
                </template>
            </ul>
        </div>
    </div>

    <p class="text-sm text-gray-500 dark:text-gray-400" x-text="displayLabel"></p>
</div>
