@php
    $statePath = $getStatePath();

    $options = collect($getOptions())
        ->map(function ($option, $value) {
            $option = is_array($option) ? $option : ['name' => $option];

            return [
                'id' => $value,
                'name' => $option['name'] ?? null,
                'native' => $option['native'] ?? ($option['name'] ?? null),
                'iso' => $option['iso'] ?? null,
                'flag' => $option['flag'] ?? null,
            ];
        })
        ->filter(fn (array $option) => $option['id'] !== null && $option['name'] !== null)
        ->values()
        ->all();

    $placeholder = $getPlaceholder() ?? __('Select languages');
    $searchPrompt = $getSearchPrompt();
    $selectedCountLabel = __('languages selected');
    $readMoreLabel = __('Read more');
    $readLessLabel = __('Read less');
    $noResultsLabel = __('No languages found.');
    $selectAllText = __('Select all');
    $deselectAllText = __('Deselect all');
    $visibleLimit = max($getVisibleLimit(), 0);
@endphp

<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div
        class="auto-translate-language-checkbox-list flex flex-col gap-3"
        x-data="(() => ({
            search: '',
            collapsed: true,
            userCollapsed: true,
            limit: {{ $visibleLimit }},
            options: {{ Js::from($options) }},
            state: $wire.{{ $applyStateBindingModifiers("\$entangle('{$statePath}')") }},
            placeholder: @js($placeholder),
            selectedCountLabel: @js($selectedCountLabel),
            readMoreLabel: @js($readMoreLabel),
            readLessLabel: @js($readLessLabel),
            noResultsLabel: @js($noResultsLabel),
            searchPrompt: @js($searchPrompt),
            selectAllText: @js($selectAllText),
            deselectAllText: @js($deselectAllText),
            get normalizedState() {
                return Array.isArray(this.state)
                    ? this.state.map((value) => String(value))
                    : []
            },
            isSelected(id) {
                return this.normalizedState.includes(String(id))
            },
            toggleSelection(id) {
                const key = String(id)
                let state = Array.isArray(this.state) ? this.state.slice() : []
                const index = state.findIndex((value) => String(value) === key)

                if (index >= 0) {
                    state.splice(index, 1)
                } else {
                    state.push(id)
                }

                this.state = state
            },
            selectAll() {
                if (this.areAllSelected) {
                    this.state = []

                    return
                }

                this.state = this.options.map((option) => option.id)
            },
            get areAllSelected() {
                return this.options.length > 0
                    && this.normalizedState.length === this.options.length
            },
            get selectAllLabel() {
                return this.areAllSelected ? this.deselectAllText : this.selectAllText
            },
            get filteredOptions() {
                if (! this.search.trim()) {
                    return this.options
                }

                const term = this.search.trim().toLowerCase()

                return this.options.filter((option) => {
                    return [option.name, option.native, option.iso]
                        .filter(Boolean)
                        .some((value) => value.toLowerCase().includes(term))
                })
            },
            get visibleOptions() {
                if (this.limit <= 0 || this.search.trim()) {
                    return this.filteredOptions
                }

                return this.collapsed
                    ? this.filteredOptions.slice(0, this.limit)
                    : this.filteredOptions
            },
            get hasMoreOptions() {
                return this.limit > 0
                    && ! this.search.trim()
                    && this.filteredOptions.length > this.limit
            },
            formatIso(option) {
                if (! option.iso) {
                    return ''
                }

                return '(' + option.iso.toUpperCase() + ')'
            },
            get selectionSummary() {
                const count = this.normalizedState.length

                if (count === 0) {
                    return this.placeholder
                }

                if (count === 1) {
                    const selected = this.options.find((option) => this.normalizedState.includes(String(option.id)))

                    if (selected) {
                        const iso = this.formatIso(selected)
                        return iso ? `${selected.native ?? selected.name} ${iso}` : (selected.native ?? selected.name)
                    }
                }

                return `${count} ${this.selectedCountLabel}`
            },
            toggleCollapsed() {
                this.collapsed = ! this.collapsed
                this.userCollapsed = this.collapsed
            },
        }))()"
        x-init="$watch('search', (value) => {
            if (value.trim()) {
                collapsed = false
            } else {
                collapsed = userCollapsed
            }
        })"
    >
        <div class="language-picker-toolbar">
            <div class="language-picker-search-wrapper">
                <input
                    type="search"
                    x-model.trim="search"
                    :placeholder="searchPrompt"
                    class="language-picker-search"
                />
            </div>
        </div>

        <p class="language-selection-summary" x-text="placeholder"></p>

        <div class="language-select-all-container">
            <button
                type="button"
                class="language-select-all"
                x-text="selectAllLabel"
                @click="selectAll()"
            ></button>
        </div>

        <ul class="language-picker-grid grid max-h-80 grid-cols-1 gap-2 overflow-y-auto pr-1 md:grid-cols-2 xl:grid-cols-3">
            <template x-for="option in visibleOptions" :key="option.id">
                <li>
                    <label
                        class="language-option-row flex items-center gap-3 rounded-xl px-3 py-2 text-left"
                        :class="{ 'language-option-row--selected': isSelected(option.id) }"
                    >
                        <input
                            type="checkbox"
                            class="language-checkbox"
                            :value="option.id"
                            :checked="isSelected(option.id)"
                            @change="toggleSelection(option.id)"
                        />

                        <div class="language-option">
                            <template x-if="option.flag">
                                <span :class="'language-flag flag-icon flag-icon-' + option.flag.toLowerCase()"></span>
                            </template>
                            <template x-if="! option.flag">
                                <span class="language-flag language-flag--placeholder"></span>
                            </template>
                            <span class="language-name">
                                <span x-text="option.native ?? option.name"></span>
                                <span class="language-iso" x-text="formatIso(option)"></span>
                            </span>
                        </div>
                    </label>
                </li>
            </template>

            <template x-if="visibleOptions.length === 0">
                <li class="col-span-full py-6 text-center text-sm text-gray-500 dark:text-gray-400" x-text="noResultsLabel"></li>
            </template>
        </ul>

        <div class="language-list-toggle-container" x-show="hasMoreOptions">
            <button type="button" class="language-list-toggle" x-text="collapsed ? readMoreLabel : readLessLabel" @click="toggleCollapsed()"></button>
        </div>
    </div>
</x-dynamic-component>
