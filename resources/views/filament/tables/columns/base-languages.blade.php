@php
    $values = $getState();
    if ($values instanceof \Illuminate\Support\Collection) {
        $values = $values->all();
    }
    if (! is_array($values)) {
        $values = array_filter([$values], fn ($v) => filled($v));
    }

    $limit = 3;
    $total = count($values);
    $extra = max($total - $limit, 0);
@endphp

<div x-data="{ expanded: false }" class="fi-ta-text grid gap-y-1">
    <div class="grid grid-cols-3 gap-1.5">
        @foreach ($values as $index => $val)
            <div x-show="expanded || {{ $index }} < {{ $limit }}" x-cloak>
                <x-filament::badge>
                    {{ is_array($val) ? json_encode($val) : $val }}
                </x-filament::badge>
            </div>
        @endforeach
    </div>

    @if ($extra > 0)
        <div class="mt-1">
            <x-filament::link color="gray" tag="div" x-on:click.prevent.stop="expanded = ! expanded">
                <span x-show="!expanded">{{ __('Show') }} {{ $extra }} {{ __('more') }}</span>
                <span x-cloak x-show="expanded">{{ __('Show') }} {{ $extra }} {{ __('less') }}</span>
            </x-filament::link>
        </div>
    @endif
</div>

