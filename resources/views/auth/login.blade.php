<x-layouts.extranet>
    <x-slot name="content">
        <div class="bg-extranet-image"></div>
        <div class="flex items-center justify-center h-full  text-lg">
            <div class="w-full md:max-w-3xl">

                <div class="flex justify-between py-4">
                    <h1 class="hidden font-bold text-2xl text-primary_ui-50 md:block" data-ts="core.login_page_title">
                        {{ t("core.login_page_title") }}
                    </h1>
                    {{-- Top-right register link --}}
                    <div class="flex justify-end mb-4">
                        <span class="text-gray-700">
                            {{ t("core.auth.dont_have_account_yet") }}
                            <a href="{{ route('register') }}" class="text-black underline decoration-[#939da2] decoration-2 [text-decoration-skip-ink:none] [text-underline-offset:3px]">
                                {{ t("core.auth.create_an_account") }}
                            </a>
                        </span>
                    </div>
                </div>


                <form method="POST" action="{{ route('login') }}" class="px-4 py-3 bg-base-100 mb-6 relative">
                    @csrf

                    <div class="min-h-[165px]">
                        {{-- Flex row for fields on desktop, stack on mobile --}}
                        <div class="flex flex-col md:flex-row md:gap-x-8">
                            <div class="flex-1 mb-4 md:mb-0">
                                <x-input.field
                                    label="{{ t('core.email') }}"
                                    type="email"
                                    name="email"
                                    value="{{ old('email') }}"
                                    autofocus="true"
                                    autocomplete="email"
                                    placeholder="<EMAIL>"
                                    class="w-full"
                                    labelClass="font-medium text-lg"
                                    max-width="w-full"
                                    inputClass="h-8 rounded-md !border-primary_ui-50 !outline-primary_ui-50"
                                />
                                @error('email')
                                    <span class="text-sm text-red-500" role="alert">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="flex-1 relative">
                                <x-input.field
                                    label="{{ t('core.auth.password') }}"
                                    type="password"
                                    name="password"
                                    class="w-full"
                                    max-width="w-full"
                                    labelClass="font-medium text-lg"
                                    inputClass="h-8 rounded-md !border-primary_ui-50 !outline-primary_ui-50"
                                />
                                {{-- Show/hide password icon (already in your component) --}}
                                {{-- @error('password')
                                    <span class="text-sm text-red-500" role="alert">{{ $message }}</span>
                                @enderror --}}
                                {{-- Forgot password below password input --}}
                                @if (Route::has('password.request'))
                                    <a href="{{ route('password.request') }}"
                                    class="inline-block mt-2 text-primary_ui-50 underline italic"
                                    style="bottom: -1.5rem;">
                                        {{ t('core.auth.forgot_password') }}
                                    </a>
                                @endif
                            </div>
                        </div>
                        {{-- Success/Error messages unchanged --}}
                        @if (session('success'))
                            <div class="p-3 text-green-600 text-center font-bold pb-14">
                                {{ session('success') }}
                            </div>
                        @endif
                        @if ($errors->has('account'))
                            <div class="p-3 text-red-600 text-center font-bold pb-14">
                                {{ $errors->first('account') }}
                            </div>
                        @endif
                        
                    @if (config('app.recaptcha_enabled'))
                        <div class="mt-3 mb-8 flex flex-col items-center justify-center">
                            {!! htmlFormSnippet() !!}

                            @error('g-recaptcha-response')
                                <span class="mt-2 block text-sm text-red-500 text-center" role="alert">{{ $message }}</span>
                            @enderror
                        </div>

                        @push('tail')
                            {!! htmlScriptTagJsApi() !!}
                        @endpush
                    @endif
                    </div>


                    {{-- Remember me, spaced left
                    <div class="flex items-center mt-8 mb-6">
                        <input class="checkbox checkbox-sm me-2" type="checkbox" name="remember"
                            id="remember" {{ old('remember') ? 'checked' : '' }}>
                        <label class="text-sm" for="remember">
                            {{ t('core.auth.remember_me') }}
                        </label>
                    </div> --}}

                    {{-- Actions: Cancel and Login --}}
                    <div class="flex items-center justify-end gap-4 mt-4 absolute bottom-0 end-0 rtl:start-auto">
                        <a href="{{ config('app.url') }}" class="text-black underline text-lg">
                            {{ t('core.cancel') }}
                        </a>
                        <x-button-link.primary-ui
                            class="!min-w-24 w-full !h-10 !text-lg !bg-primary_ui-50 !text-white font-bold rounded-none uppercase"
                            elementType="button"
                            type="submit"
                        >
                            {{ t('core.auth.login') }}
                        </x-button-link.primary-ui>
                    </div>
                </form>
            </div>
        </div>
    </x-slot>
</x-layouts.extranet>
