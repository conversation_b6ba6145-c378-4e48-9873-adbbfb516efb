<x-layouts.focus>
    <x-slot name="left">
        <div class="flex flex-col py-2 md:py-10 gap-4 h-full">
            <div class="">
                <h1 class="text-start font-bold text-xl">{{ t('core.auth.2fa') }}</h1>
            </div>
            <div class="card w-full md:max-w-xl bg-base-100 shadow-xl p-4 md:p-8">
                @if (! \App\Support\TwoFactor::isAvailable())
                    <div class="flex flex-col gap-4">
                        <p class="text-sm text-gray-700">
                            {{ __('Two-factor authentication is currently disabled for this application. You can sign in without a verification code.') }}
                        </p>

                        <x-button-link.primary class="inline-block !w-full" :href="route('login')">
                            {{ __('Back to login') }}
                        </x-button-link.primary>
                    </div>
                @else
                    <form method="POST">
                        @csrf

                        <x-input.field label="{{ t('core.auth.2fa_code') }}" name="{{ $input }}" id="{{ $input }}"
                                       required autofocus="true" class="my-2"
                                       minlength="6" placeholder="123456" required max-width="w-full"/>

                        @if($errors->isNotEmpty())
                            @foreach ($errors->all() as $error)
                                <span class="text-xs text-red-500" role="alert">
                                    {{ $error }}
                                </span>
                            @endforeach
                        @endif

                        <p class="text-xs mt-2">
                            {{ t('core.auth.2fa_open_app') }}
                        </p>

                        <p class="text-xs mt-2">
                            {{ t('core.auth.2fa_lost_access') }}
                        </p>

                        <div class="flex flex-wrap gap-8 justify-end items-center">
                            <div>
                                <a href="/" class="text-primary-950">{{ t('core.auth.cancel') }}</a>
                            </div>
                            <div class="basis-1/2">
                                <x-button-link.primary class="inline-block !w-full my-2" elementType="button" type="submit">
                                    {{ t('core.auth.verify') }}
                                </x-button-link.primary>
                            </div>
                        </div>

                    </form>
                @endif
            </div>
        </div>
    </x-slot>

</x-layouts.focus>
