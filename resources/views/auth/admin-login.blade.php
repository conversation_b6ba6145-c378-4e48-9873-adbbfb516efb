<x-layouts.extranet-admin>
  <x-slot name="content">
      <div class="flex items-center py-2 md:py-10 gap-4 h-full w-full justify-center">
          <div class="w-full md:max-w-xl bg-base-100 shadow-regular p-4 md:p-8">
              @if (session('success'))
                  <div class="mb-4 p-3 rounded bg-green-100 text-green-800 border border-green-300">
                      {{ session('success') }}
                  </div>
              @endif
              @if ($errors->has('account'))
                  <div class="mb-4 p-3 rounded bg-red-100 text-red-800 border border-red-300">
                      {{ $errors->first('account') }}
                  </div>
              @endif
              <form method="POST" action="{{ route('login') }}">
                  @csrf

                  <div class="flex justify-between">
                      <h1 class="text-start font-bold text-xl">{{ __('app.auth.login') }}</h1>
                      <p class="text-sm mt-2 text-end">{{__('app.auth.no_account')}} <a class="text-primary-950 font-bold underline" href="{{ route('register') }}">{{__('app.auth.register')}}</a></p>
                  </div>

                  <x-input.field label="{{ __('app.auth.email') }}" type="email" name="email"
                              value="{{ old('email') }}" autofocus="true" class="my-2"
                              autocomplete="email" max-width="w-full"/>
                  @error('email')
                  <span class="text-sm text-red-500" role="alert">
                          {{ $message }}
                      </span>
                  @enderror

                  <x-input.field label="{{ __('app.auth.password') }}" type="password" name="password" class="my-2"  max-width="w-full"/>

                  @error('password')
                      <span class="text-sm text-red-500" role="alert">
                          {{ $message }}
                      </span>
                  @enderror

                  {{-- Recaptcha removed for parity with standard login functionality --}}

                  {{-- Remember me removed for parity with standard login functionality --}}
                  <div class="my-3 flex flex-wrap gap-2 justify-between text-sm">
                      <div></div>
                      <div>
                          @if (Route::has('password.request'))
                              <a class="text-primary-950 text-sm underline italic" href="{{ route('password.request') }}">
                                  {{ __('app.auth.forgot_password') }}
                              </a>
                          @endif
                      </div>
                  </div>

                  <div class="flex flex-wrap gap-8 justify-end items-center">
                      <div>
                          <a href="{{config('app.url')}}" class="text-primary-950 ">{{ __('app.auth.cancel') }}</a>
                      </div>
                      <div class="basis-1/2">
                          <x-button-link.primary-ui class="inline-block !w-full my-2" elementType="button" type="submit">
                              {{ __('app.navigation.login') }}
                          </x-button-link.primary-ui>
                      </div>
                  </div>

                  {{-- <x-auth.social-login>
                      <x-slot name="before">
                          <div class="flex flex-col w-full">
                              <div class="divider">{{ __('or') }}</div>
                          </div>
                      </x-slot>
                  </x-auth.social-login> --}}

              </form>
          </div>
      </div>
  </x-slot>
</x-layouts.extranet-admin>
