@import "/vendor/filament/filament/resources/css/theme.css";
@import "../../flag-icons.min.css";

@config "tailwind.config.js";

.permission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.permission-category {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
}

.dark .permission-category {
  background: #1f2937;
  border-color: #374151;
}


/* Auto-translate language picker */
.auto-translate-language-checkbox-list {
  position: relative;
}

.auto-translate-language-checkbox-list .language-picker-toolbar {
  display: block;
  margin-bottom: 0.25rem;
}

.auto-translate-language-checkbox-list .language-picker-search-wrapper {
  flex: 1 1 16rem;
  min-width: 12rem;
}

.auto-translate-language-checkbox-list .language-picker-search {
  width: 100%;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 0.25rem;
  box-shadow: none;
  padding: 0.5rem 0.75rem;
  color: #334155;
}

.auto-translate-language-checkbox-list .language-picker-search:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 1px rgba(37, 99, 235, 0.25);
}

.dark .auto-translate-language-checkbox-list .language-picker-search {
  background-color: #0f172a;
  border-color: #334155;
  color: #e2e8f0;
}

.dark .auto-translate-language-checkbox-list .language-picker-search::placeholder {
  color: #94a3b8;
}

.auto-translate-language-checkbox-list .language-selection-summary {
  font-size: 0.85rem;
  font-weight: 500;
  color: #475569;
}

.dark .auto-translate-language-checkbox-list .language-selection-summary {
  color: #cbd5f5;
}

.auto-translate-language-checkbox-list .language-picker-grid {
  gap: 0.5rem;
}

.auto-translate-language-checkbox-list .language-option-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-radius: 0.75rem;
  transition: background-color 150ms ease, box-shadow 150ms ease;
}

.auto-translate-language-checkbox-list .language-option-row:hover {
  background-color: transparent;
}

.dark .auto-translate-language-checkbox-list .language-option-row:hover {
  background-color: transparent;
}

.auto-translate-language-checkbox-list .language-option-row--selected {
  background-color: transparent;
  box-shadow: none;
}

.dark .auto-translate-language-checkbox-list .language-option-row--selected {
  background-color: transparent;
  box-shadow: none;
}


.auto-translate-language-checkbox-list .language-select-all-container {
  display: flex;
  justify-content: flex-start;
}

.auto-translate-language-checkbox-list .language-select-all {
  background: none;
  border: none;
  color: #f59e0b;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
}

.auto-translate-language-checkbox-list .language-select-all:hover,
.auto-translate-language-checkbox-list .language-select-all:focus {
  text-decoration: underline;
  outline: none;
}

.dark .auto-translate-language-checkbox-list .language-select-all {
  color: #facc15;
}

.auto-translate-language-checkbox-list .language-checkbox {
  accent-color: #2563eb;
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.dark .auto-translate-language-checkbox-list .language-checkbox {
  accent-color: #93c5fd;
}

.auto-translate-language-checkbox-list .language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.auto-translate-language-checkbox-list .language-flag {
  width: 1.75rem;
  height: 1.25rem;
  border-radius: 0.3rem;
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
}

.auto-translate-language-checkbox-list .language-flag--placeholder {
  background-color: #e2e8f0;
}

.dark .auto-translate-language-checkbox-list .language-flag--placeholder {
  background-color: #475569;
}

.auto-translate-language-checkbox-list .language-name {
  flex: 1 1 auto;
  display: flex;
  align-items: baseline;
  gap: 0.35rem;
  font-weight: 400;
  color: #0f172a;
}

.dark .auto-translate-language-checkbox-list .language-name {
  color: #f8fafc;
}

.auto-translate-language-checkbox-list .language-iso {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: #475569;
}

.dark .auto-translate-language-checkbox-list .language-iso {
  color: #cbd5f5;
}



.dark .auto-translate-language-checkbox-list .language-option-row--selected .language-iso {
  color: #93c5fd;
}

.auto-translate-language-checkbox-list .language-list-toggle-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.75rem;
}

.auto-translate-language-checkbox-list .language-list-toggle {
  background: none;
  border: none;
  color: #1d4ed8;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.25rem 0;
}

.auto-translate-language-checkbox-list .language-list-toggle:hover,
.auto-translate-language-checkbox-list .language-list-toggle:focus {
  text-decoration: underline;
  outline: none;
}

.dark .auto-translate-language-checkbox-list .language-list-toggle {
  color: #93c5fd;
}

.email-template-key-input input[readonly] {
  background-color: #f1f5f9 !important;
 
  cursor: not-allowed !important;
}

.email-template-key-input {
  background-color: #f1f5f9 !important;
}

.email-template-key-input:hover,
.email-template-key-input:focus-within {
  cursor: not-allowed !important;
  box-shadow: none !important;
}

.email-template-key-input input[readonly]:hover {
  cursor: not-allowed !important;
}

.dark .email-template-key-input input[readonly] {
  background-color: #1f2937 !important;
  color: #64748b !important;
}

.dark .email-template-key-input {
  background-color: #1f2937 !important;
  border-color: #334155 !important;
}
