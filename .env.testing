APP_NAME="TallStack"
APP_DESCRIPTION="TallStack is a SaaS starter kit that helps you build and launch your SaaS product faster."
APP_ENV=testing
APP_KEY=base64:TIA4x01Yf6m5oszm43QVDoD91xkPAobYBsVdU4AZpm0=
APP_DEBUG=true
APP_URL=http://localhost:5500
APP_TIMEZONE=UTC

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=saasykit_test
DB_USERNAME=root
DB_PASSWORD=

# MongoDB (for notifications)
DB_MONGO_CONNECTION=mongodb
DB_MONGO_HOST=127.0.0.1
DB_MONGO_PORT=27017
DB_MONGO_DATABASE=dev_notifications_db
DB_MONGO_USERNAME=
DB_MONGO_PASSWORD=

BROADCAST_DRIVER=reverb
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""

FACEBOOK_CLIENT_ID=""
FACEBOOK_CLIENT_SECRET=""

STRIPE_SECRET_KEY="sk_test_123123123123123123123123123123"
STRIPE_PUBLISHABLE_KEY=""
STRIPE_WEBHOOK_SIGNING_SECRET="1234"
STRIPE_EVENT_URL="https://dashboard.stripe.com/tests/events/{{eventId}}"
STRIPE_CUSTOMER_URL="https://dashboard.stripe.com/test/customers/{{customerId}}"
STRIPE_ADMIN_WEBHOOK_EVENTS_EMAIL_RECIPIENTS=""

PADDLE_VENDOR_ID="1234"
PADDLE_CLIENT_SIDE_TOKEN="1234"
PADDLE_VENDOR_AUTH_CODE="1234"
PADDLE_PUBLIC_KEY="123"
PADDLE_WEBHOOK_SECRET="1234"
PADDLE_IS_SANDBOX=true

# local development settings
APP_PORT=8080

SAIL_XDEBUG_MODE=develop,debug,coverage

TRANSLATION_API_URL="http://localhost:3000"
TRANSLATION_API_KEY="123"
CMS_URL= "http://localhost:3001"
GOTENBERG_URL='http://************:42030'
ALLOWED_ORIGINS=http://localhost:4200,http://localhost:5200

MEDIA_MANAGER_API='http://*************:8000'
