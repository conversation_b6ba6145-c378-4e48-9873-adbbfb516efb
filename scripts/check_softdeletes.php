<?php
require __DIR__ . '/../vendor/autoload.php';

use Illuminate\Database\Eloquent\SoftDeletes;

$softDeleteModels = [];

$baseDir = realpath(__DIR__ . '/../app/Models');
$iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($baseDir));

foreach ($iterator as $file) {
    if ($file->getExtension() !== 'php') {
        continue;
    }

    $relative = substr($file->getPathname(), strlen($baseDir) + 1);
    $class = 'App\\Models\\' . str_replace(['/', '.php'], ['\\', ''], $relative);

    if (!class_exists($class)) {
        continue;
    }

    $rc = new ReflectionClass($class);

    if ($rc->isAbstract()) {
        continue;
    }

    if (!in_array(SoftDeletes::class, $rc->getTraitNames(), true)) {
        continue;
    }

    /** @var \Illuminate\Database\Eloquent\Model $model */
    $model = $rc->newInstanceWithoutConstructor();
    $softDeleteModels[$class] = $model->getTable();
}

$missing = [];
$migrations = glob(__DIR__ . '/../database/migrations/*.php');

foreach ($softDeleteModels as $class => $table) {
    $patternCreate = "Schema::create('" . $table . "'";
    $patternTable = "Schema::table('" . $table . "'";
    $found = false;

    foreach ($migrations as $file) {
        $contents = file_get_contents($file);

        if (
            (strpos($contents, $patternCreate) !== false || strpos($contents, $patternTable) !== false)
            && (strpos($contents, 'softDeletes(') !== false || strpos($contents, 'dropSoftDeletes(') !== false)
        ) {
            $found = true;
            break;
        }
    }

    if (!$found) {
        $missing[$class] = $table;
    }
}

foreach ($missing as $class => $table) {
    echo $class . ' => ' . $table . PHP_EOL;
}
