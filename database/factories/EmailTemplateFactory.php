<?php

namespace Database\Factories;

use App\Constants\TemplateStatus;
use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<EmailTemplate>
 */
class EmailTemplateFactory extends Factory
{
    protected $model = EmailTemplate::class;

    public function definition(): array
    {
        return [
            'key' => $this->faker->unique()->slug,
            'name' => $this->faker->sentence(3),
            'description' => $this->faker->optional()->paragraph,
            'language_id' => 1,
            'locale' => 'en',
            'status' => TemplateStatus::ACTIVE,
            'is_shared' => false,
            'head' => $this->faker->optional()->text(200),
            'content' => $this->faker->paragraphs(3, true),
            'foot' => $this->faker->optional()->text(200),
            'style' => $this->faker->optional()->text(200),
            'script' => $this->faker->optional()->text(200),
            'created_by' => User::factory(),
        ];
    }

    public function draft(): self
    {
        return $this->state(fn () => [
            'status' => TemplateStatus::DRAFT,
        ]);
    }
}
