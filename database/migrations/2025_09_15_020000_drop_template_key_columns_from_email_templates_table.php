<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('email_templates', function (Blueprint $table): void {
            if (Schema::hasColumn('email_templates', 'head_template_key')) {
                $table->dropIndex('email_templates_head_template_key_index');
            }

            if (Schema::hasColumn('email_templates', 'content_template_key')) {
                $table->dropIndex('email_templates_content_template_key_index');
            }

            if (Schema::hasColumn('email_templates', 'foot_template_key')) {
                $table->dropIndex('email_templates_foot_template_key_index');
            }

            $columnsToDrop = array_filter([
                Schema::hasColumn('email_templates', 'head_template_key') ? 'head_template_key' : null,
                Schema::hasColumn('email_templates', 'content_template_key') ? 'content_template_key' : null,
                Schema::hasColumn('email_templates', 'foot_template_key') ? 'foot_template_key' : null,
            ]);

            if (! empty($columnsToDrop)) {
                $table->dropColumn($columnsToDrop);
            }
        });
    }

    public function down(): void
    {
        Schema::table('email_templates', function (Blueprint $table): void {
            $table->string('head_template_key')
                ->nullable()
                ->after('head');
            $table->string('content_template_key')
                ->nullable()
                ->after('content');
            $table->string('foot_template_key')
                ->nullable()
                ->after('foot');

            $table->index('head_template_key');
            $table->index('content_template_key');
            $table->index('foot_template_key');
        });
    }
};
