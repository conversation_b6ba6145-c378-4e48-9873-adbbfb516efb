<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('email_templates', 'language_id')) {
            Schema::table('email_templates', function (Blueprint $table) {
                $table->unsignedBigInteger('language_id')
                    ->nullable()
                    ->after('description')
                    ->comment('Identifier of the language selected for the template');

                $table->index('language_id', 'email_templates_language_id_index');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('email_templates', 'language_id')) {
            Schema::table('email_templates', function (Blueprint $table) {
                $table->dropIndex('email_templates_language_id_index');
                $table->dropColumn('language_id');
            });
        }
    }
};
