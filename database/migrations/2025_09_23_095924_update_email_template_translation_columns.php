<?php

use App\Services\TranslationApiService;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('email_templates', function (Blueprint $table): void {
            $table->string('key')->nullable()->change();
        });

        DB::table('email_templates')
            ->whereNotNull('id_row')
            ->update(['key' => null]);

        $languageIsoMap = [];

        if (class_exists(TranslationApiService::class)) {
            try {
                $apiService = app(TranslationApiService::class);
                $response = $apiService->getAllLanguages(['limit' => 1000]);
                $items = $response['data'] ?? $response ?? [];

                foreach ($items as $language) {
                    if (! isset($language['id'])) {
                        continue;
                    }

                    $iso = strtolower((string) ($language['languageISO2'] ?? ''));

                    if ($iso === '') {
                        continue;
                    }

                    $languageIsoMap[(int) $language['id']] = $iso;
                }
            } catch (\Throwable $exception) {
                $languageIsoMap = [];
            }
        }

        if (! empty($languageIsoMap)) {
            foreach ($languageIsoMap as $languageId => $iso) {
                DB::table('email_templates')
                    ->where('language_id', $languageId)
                    ->update(['locale' => $iso]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('email_templates as child')
            ->join('email_templates as parent', 'child.id_row', '=', 'parent.id')
            ->whereNotNull('child.id_row')
            ->update([
                'child.key' => DB::raw('parent.key'),
                'child.locale' => null,
            ]);

        Schema::table('email_templates', function (Blueprint $table): void {
            $table->string('key')->nullable(false)->change();
        });
    }
};
