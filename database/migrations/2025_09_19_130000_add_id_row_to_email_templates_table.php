<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (! Schema::hasColumn('email_templates', 'id_row')) {
            Schema::table('email_templates', function (Blueprint $table): void {
                $table->unsignedBigInteger('id_row')
                    ->nullable()
                    ->after('metadata');

                $table->foreign('id_row')
                    ->references('id')
                    ->on('email_templates')
                    ->nullOnDelete();
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasColumn('email_templates', 'id_row')) {
            Schema::table('email_templates', function (Blueprint $table): void {
                $table->dropForeign(['id_row']);
                $table->dropColumn('id_row');
            });
        }
    }
};
