<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private function addSoftDeletes(string $tableName, callable $callback): void
    {
        if (! Schema::hasColumn($tableName, 'deleted_at')) {
            $callback();
        }
    }

    private function dropSoftDeletes(string $tableName, callable $callback): void
    {
        if (Schema::hasColumn($tableName, 'deleted_at')) {
            $callback();
        }
    }

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->addSoftDeletes('contact_platforms', function () {
            Schema::table('contact_platforms', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('allowed_routes', function () {
            Schema::table('allowed_routes', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('blog_post_categories', function () {
            Schema::table('blog_post_categories', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('user_subscription_trials', function () {
            Schema::table('user_subscription_trials', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('configs', function () {
            Schema::table('configs', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('user_parameters', function () {
            Schema::table('user_parameters', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('announcements', function () {
            Schema::table('announcements', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('user_contact_platform_informations', function () {
            Schema::table('user_contact_platform_informations', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('carts', function () {
            Schema::table('carts', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('blog_posts', function () {
            Schema::table('blog_posts', function (Blueprint $table) {
                $table->softDeletes();
            });
        });

        $this->addSoftDeletes('user_stripe_data', function () {
            Schema::table('user_stripe_data', function (Blueprint $table) {
                $table->softDeletes();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->dropSoftDeletes('contact_platforms', function () {
            Schema::table('contact_platforms', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('allowed_routes', function () {
            Schema::table('allowed_routes', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('blog_post_categories', function () {
            Schema::table('blog_post_categories', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('user_subscription_trials', function () {
            Schema::table('user_subscription_trials', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('configs', function () {
            Schema::table('configs', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('user_parameters', function () {
            Schema::table('user_parameters', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('announcements', function () {
            Schema::table('announcements', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('user_contact_platform_informations', function () {
            Schema::table('user_contact_platform_informations', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('carts', function () {
            Schema::table('carts', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('blog_posts', function () {
            Schema::table('blog_posts', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });

        $this->dropSoftDeletes('user_stripe_data', function () {
            Schema::table('user_stripe_data', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        });
    }
};
