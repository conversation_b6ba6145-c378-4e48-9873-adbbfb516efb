<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (! Schema::hasTable('email_templates')) {
            return;
        }

        $hasLegacy = $this->indexExists('email_templates', 'email_templates_key_unique');
        $hasComposite = $this->indexExists('email_templates', 'email_templates_key_language_id_unique');

        Schema::table('email_templates', function (Blueprint $table) use ($hasLegacy, $hasComposite): void {
            if ($hasLegacy) {
                $table->dropUnique('email_templates_key_unique');
            }

            if (! $hasComposite) {
                $table->unique(['key', 'language_id'], 'email_templates_key_language_id_unique');
            }
        });
    }

    public function down(): void
    {
        if (! Schema::hasTable('email_templates')) {
            return;
        }

        $hasComposite = $this->indexExists('email_templates', 'email_templates_key_language_id_unique');
        $hasLegacy = $this->indexExists('email_templates', 'email_templates_key_unique');

        Schema::table('email_templates', function (Blueprint $table) use ($hasComposite, $hasLegacy): void {
            if ($hasComposite) {
                $table->dropUnique('email_templates_key_language_id_unique');
            }

            if (! $hasLegacy) {
                $table->unique('key', 'email_templates_key_unique');
            }
        });
    }

    private function indexExists(string $table, string $indexName): bool
    {
        $database = Schema::getConnection()->getDatabaseName();

        $result = DB::select(
            'SELECT COUNT(1) AS aggregate FROM information_schema.statistics WHERE table_schema = ? AND table_name = ? AND index_name = ?',
            [$database, $table, $indexName]
        );

        return ($result[0]->aggregate ?? 0) > 0;
    }
};
