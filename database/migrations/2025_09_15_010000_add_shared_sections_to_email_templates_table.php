<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->boolean('is_shared')
                ->default(false)
                ->after('status');
            $table->string('head_template_key')
                ->nullable()
                ->after('head');
            $table->string('content_template_key')
                ->nullable()
                ->after('content');
            $table->string('foot_template_key')
                ->nullable()
                ->after('foot');

            $table->index('head_template_key');
            $table->index('content_template_key');
            $table->index('foot_template_key');
        });
    }

    public function down(): void
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->dropIndex(['head_template_key']);
            $table->dropIndex(['content_template_key']);
            $table->dropIndex(['foot_template_key']);

            $table->dropColumn([
                'is_shared',
                'head_template_key',
                'content_template_key',
                'foot_template_key',
            ]);
        });
    }
};
