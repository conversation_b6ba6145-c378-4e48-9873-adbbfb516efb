<?php

use App\Constants\TemplateStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('language_id')->nullable();
            $table->string('locale');
            $table->enum('status', array_map(static fn (TemplateStatus $status) => $status->value, TemplateStatus::cases()))
                ->nullable()
                ->default(TemplateStatus::DRAFT->value);
            $table->text('head')->nullable();
            $table->text('content')->nullable();
            $table->text('foot')->nullable();
            $table->text('style')->nullable();
            $table->text('script')->nullable();
            $table->foreignId('created_by')->nullable()
                ->constrained('users')
                ->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();

            $table->index('language_id', 'email_templates_language_id_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
};
