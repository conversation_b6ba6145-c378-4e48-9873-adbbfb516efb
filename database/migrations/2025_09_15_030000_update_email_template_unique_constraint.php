<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('email_templates', function (Blueprint $table): void {
            if (Schema::hasColumn('email_templates', 'key')) {
                try {
                    $table->dropUnique('email_templates_key_unique');
                } catch (\Throwable $exception) {
                    // The unique index might not exist yet (fresh database)
                }

                $table->unique(['key', 'language_id'], 'email_templates_key_language_id_unique');
            }
        });
    }

    public function down(): void
    {
        Schema::table('email_templates', function (Blueprint $table): void {
            if (Schema::hasColumn('email_templates', 'key')) {
                try {
                    $table->dropUnique('email_templates_key_language_id_unique');
                } catch (\Throwable $exception) {
                    // Index already removed or never created
                }

                $table->unique('key', 'email_templates_key_unique');
            }
        });
    }
};
