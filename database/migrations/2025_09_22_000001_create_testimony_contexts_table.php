<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('testimony_contexts', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('slug')->unique();
            $table->timestamps();
        });

        // Seed initial contexts matching the legacy enum values
        DB::table('testimony_contexts')->insertOrIgnore([
            [
                'name' => 'Global',
                'slug' => 'global',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Expertise',
                'slug' => 'expertise',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    public function down(): void
    {
        Schema::dropIfExists('testimony_contexts');
    }
};

