<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Tables that should have a soft delete column but don't yet.
     */
    private array $tables = [
        'currencies',
        'discount_code_redemptions',
        'discount_codes',
        'discount_one_time_product',
        'discount_payment_provider_data',
        'discount_plan',
        'discounts',
        'email_providers',
        'failed_jobs',
        'feature_product',
        'features',
        'intervals',
        'invoices',
        'ip_blocks',
        'jobs',
        'media',
        'metric_data',
        'metrics',
        'oauth_login_providers',
        'one_time_product_payment_provider_data',
        'one_time_product_price_payment_provider_data',
        'one_time_product_prices',
        'one_time_products',
        'order_discounts',
        'order_items',
        'orders',
        'password_reset_tokens',
        'password_resets',
        'payment_providers',
        'personal_access_tokens',
        'plan_meter_payment_provider_data',
        'plan_meters',
        'plan_payment_provider_data',
        'plan_price_payment_provider_data',
        'plan_prices',
        'plans',
        'products',
        'receipts',
        'regions',
        'roadmap_item_user_upvotes',
        'roadmap_items',
        'role_apps',
        'routes',
        'stripe_events',
        'subscription_discounts',
        'subscription_usages',
        'subscription_versions',
        'subscriptions',
        'testimony_contexts',
        'transaction_versions',
        'transactions',
        'uploads',
        'verification_providers',
    ];

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        foreach ($this->tables as $tableName) {
            if (! Schema::hasTable($tableName)) {
                continue;
            }

            if (Schema::hasColumn($tableName, 'deleted_at')) {
                continue;
            }

            Schema::table($tableName, function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        foreach ($this->tables as $tableName) {
            if (! Schema::hasTable($tableName)) {
                continue;
            }

            if (! Schema::hasColumn($tableName, 'deleted_at')) {
                continue;
            }

            Schema::table($tableName, function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }
    }
};
