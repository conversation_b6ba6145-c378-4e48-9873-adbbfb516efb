<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (! Schema::hasTable('email_templates')) {
            return;
        }

        $dropLegacyIndex = $this->indexExists('email_templates', 'email_templates_key_unique');
        $hasCompositeIndex = $this->indexExists('email_templates', 'email_templates_key_language_id_unique');

        Schema::table('email_templates', function (Blueprint $table) use ($dropLegacyIndex, $hasCompositeIndex): void {
            if ($dropLegacyIndex) {
                $table->dropUnique('email_templates_key_unique');
            }

            if (! $hasCompositeIndex) {
                $table->unique(['key', 'language_id'], 'email_templates_key_language_id_unique');
            }
        });
    }

    public function down(): void
    {
        if (! Schema::hasTable('email_templates')) {
            return;
        }

        $hasCompositeIndex = $this->indexExists('email_templates', 'email_templates_key_language_id_unique');
        $hasLegacyIndex = $this->indexExists('email_templates', 'email_templates_key_unique');

        Schema::table('email_templates', function (Blueprint $table) use ($hasCompositeIndex, $hasLegacyIndex): void {
            if ($hasCompositeIndex) {
                $table->dropUnique('email_templates_key_language_id_unique');
            }

            if (! $hasLegacyIndex) {
                $table->unique('key', 'email_templates_key_unique');
            }
        });
    }

    private function indexExists(string $table, string $indexName): bool
    {
        $database = Schema::getConnection()->getDatabaseName();

        $result = DB::select(
            'SELECT COUNT(1) AS aggregate FROM information_schema.statistics WHERE table_schema = ? AND table_name = ? AND index_name = ?',
            [$database, $table, $indexName]
        );

        return ($result[0]->aggregate ?? 0) > 0;
    }
};
