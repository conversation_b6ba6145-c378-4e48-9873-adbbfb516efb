<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('testimonies', function (Blueprint $table) {
            $table->unsignedBigInteger('context_id')->nullable()->after('is_default');
        });

        // Map legacy enum/string 'context' to the new relation if the column exists
        if (Schema::hasColumn('testimonies', 'context')) {
            $contexts = DB::table('testimony_contexts')->get()->keyBy('slug');

            DB::table('testimonies')->orderBy('id')
                ->chunkById(500, function ($rows) use ($contexts) {
                    foreach ($rows as $row) {
                        $slug = $row->context ?? 'global';
                        $ctxId = $contexts[$slug]->id ?? null;
                        if ($ctxId) {
                            DB::table('testimonies')->where('id', $row->id)->update(['context_id' => $ctxId]);
                        }
                    }
                });
        }

        // Ensure any remaining nulls get default to 'global'
        $globalId = DB::table('testimony_contexts')->where('slug', 'global')->value('id');
        if ($globalId) {
            DB::table('testimonies')->whereNull('context_id')->update(['context_id' => $globalId]);
        }

        Schema::table('testimonies', function (Blueprint $table) {
            $table->foreign('context_id')->references('id')->on('testimony_contexts')->cascadeOnUpdate()->nullOnDelete();
            $table->index('context_id');
        });

        // Drop the legacy column if it exists
        if (Schema::hasColumn('testimonies', 'context')) {
            Schema::table('testimonies', function (Blueprint $table) {
                $table->dropColumn('context');
            });
        }

        // Keep column nullable to avoid requiring doctrine/dbal; form enforces selection
    }

    public function down(): void
    {
        // Add back legacy enum column
        Schema::table('testimonies', function (Blueprint $table) {
            if (! Schema::hasColumn('testimonies', 'context')) {
                $table->enum('context', ['global', 'expertise'])->nullable()->default('global');
            }
        });

        // Map relation back to legacy enum value using slug
        if (Schema::hasColumn('testimonies', 'context_id')) {
            $ctxMap = DB::table('testimony_contexts')->pluck('slug', 'id');
            DB::table('testimonies')->orderBy('id')
                ->chunkById(500, function ($rows) use ($ctxMap) {
                    foreach ($rows as $row) {
                        $slug = $ctxMap[$row->context_id] ?? 'global';
                        DB::table('testimonies')->where('id', $row->id)->update(['context' => $slug]);
                    }
                });
        }

        Schema::table('testimonies', function (Blueprint $table) {
            if (Schema::hasColumn('testimonies', 'context_id')) {
                $table->dropForeign(['context_id']);
                $table->dropIndex(['context_id']);
                $table->dropColumn('context_id');
            }
        });

        // Optionally drop contexts table in separate migration's down if needed
    }
};
