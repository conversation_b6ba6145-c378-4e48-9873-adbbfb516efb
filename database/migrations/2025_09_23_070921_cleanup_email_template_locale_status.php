<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        DB::table('email_templates')
            ->whereNotNull('id_row')
            ->update([
                'status' => null,
                'locale' => null,
            ]);
    }

    public function down(): void
    {
        // intentionally left blank: previous values cannot be restored reliably
    }
};
